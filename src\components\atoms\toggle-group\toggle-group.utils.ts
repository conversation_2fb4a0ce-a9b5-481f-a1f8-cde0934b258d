import type { ToggleSize, ToggleVariant } from './toggle-group.types';

export function getToggleVariantClasses(props: { variant?: ToggleVariant; size?: ToggleSize }): string {
  const { variant = 'default', size = 'md' } = props;

  const baseClasses = [
    'inline-flex items-center justify-center rounded-md',
    'text-sm font-medium transition-all',
    'focus-visible:outline-none focus-visible:ring-2',
    'disabled:pointer-events-none disabled:opacity-50',
    'border-2',
  ];

  const variantClasses: Record<ToggleVariant, string[]> = {
    default: ['border-transparent', 'hover:border-[#E4E4E7]', 'data-[state=on]:border-[#272727]'],
    outline: ['border-[#E4E4E7]', 'hover:border-[#D4D4D8]', 'data-[state=on]:border-[#272727]'],
    ghost: ['border-transparent', 'hover:border-[#F4F4F5]', 'data-[state=on]:border-[#272727]'],
  };

  const sizeClasses: Record<ToggleSize, string[]> = {
    sm: ['h-8', 'px-2.5', 'text-xs'],
    md: ['h-9', 'px-3', 'text-sm'],
    lg: ['h-10', 'px-4', 'text-base'],
  };

  return [...baseClasses, ...variantClasses[variant], ...sizeClasses[size]].join(' ');
}

export function getSelectedValues(
  value: string | string[] | undefined,
  defaultValue: string | string[] | undefined
): string[] {
  if (value !== undefined) {
    return Array.isArray(value) ? value : value ? [value] : [];
  }
  if (defaultValue !== undefined) {
    return Array.isArray(defaultValue) ? defaultValue : defaultValue ? [defaultValue] : [];
  }
  return [];
}

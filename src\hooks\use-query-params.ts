export interface QueryParams {
  tenantId: number;
  sid: string;
  collectionName: string;
  permalink: string;
  page: number;
  perPage: number;
  sizeSystem: string;
  currency: string;
  baseUrl: string;
  lang: string;
  similarityThreshold: number;
  personaHash?: string;
}

export function useQueryParams() {
  const search = window.location.search;
  const searchParams = new URLSearchParams(search);

  const params: QueryParams = {
    tenantId: Number(searchParams.get('tenantId')),
    sid: searchParams.get('sessionId') || '',
    collectionName: searchParams.get('collectionName') || '',
    permalink: searchParams.get('permalink') || '',
    page: Number(searchParams.get('page')),
    perPage: Number(searchParams.get('perPage')),
    sizeSystem: searchParams.get('sizeSystem') || '',
    currency: searchParams.get('currency') || '',
    baseUrl: searchParams.get('baseUrl') || '',
    lang: searchParams.get('lang') || '',
    similarityThreshold: Number(searchParams.get('similarityThreshold')),
    personaHash: searchParams.get('personaHash') || undefined,
  };

  const isValid = Boolean(
    params.tenantId &&
      params.sid &&
      params.collectionName &&
      params.permalink &&
      params.page &&
      params.perPage &&
      params.sizeSystem &&
      params.currency &&
      params.baseUrl &&
      params.lang
  );

  return {
    raw: params,
    isValid,
  };
}

name: Deploy storybook

on:
  push:
    branches:
      - develop

jobs:
  storybook:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '22.13.1'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: Install Dependencies
        run: pnpm install

      - name: Build Storybook
        run: pnpm build-storybook

      - name: Deploy Storybook
        run: |
          echo "Insert here the code to deploy"

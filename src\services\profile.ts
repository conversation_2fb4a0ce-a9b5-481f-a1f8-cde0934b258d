export interface Profile {
  sessionId: string;
  currentProfile: CurrentProfile;
}

export interface CurrentProfile {
  id: string;
  measures: Measures;
}

export interface Measures {
  gender: string;
  weight: number;
  height: string;
  age: number;
}

export async function getProfile(): Promise<Profile> {
  const urlParams = new URLSearchParams(window.location.search);
  const sessionId = urlParams.get('sessionId');
  const baseUrl = urlParams.get('baseUrl');

  if (!sessionId || !baseUrl) {
    throw new Error('Missing sessionId or baseUrl in query parameters');
  }

  const response = await fetch(`${baseUrl}api/me?sid=${sessionId}`);
  if (!response.ok) {
    throw new Error('Network response was not ok');
  }

  const data = await response.json();
  const measures = data?.currentProfile?.measures;

  return {
    sessionId: sessionId,
    currentProfile: {
      id: data?.currentProfile?.id,
      measures: {
        height: measures?.height,
        weight: measures?.weight,
        age: measures?.age,
        gender: measures?.gender,
      },
    },
  };
}

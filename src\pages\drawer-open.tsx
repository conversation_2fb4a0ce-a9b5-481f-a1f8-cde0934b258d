import { useEffect, useState } from 'react';
import { Drawer, DrawerContent } from '@/components/molecules';
import { SizeRecommendationForm } from '@/components/organisms';
import { postMessageToParent, usePostMessage } from '@/hooks/use-post-message';

export function DrawerOpenPage() {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const t = setTimeout(() => setIsOpen(true), 100);
    return () => clearTimeout(t);
  }, []);

  const handleClickOutside = () => {
    setIsOpen(false);
    setTimeout(() => {
      postMessageToParent({ id: 'szb_toggle_drawer' });
    }, 1000);
  };

  const handleClose = () => {
    setIsOpen(false);
    postMessageToParent({ id: 'szb_close_fullscreen_modal' });
  };

  const handleResetRecommendation = () => {
    handleClose();
    postMessageToParent({ id: 'szb_reset_persona_hash' });
  };

  usePostMessage(
    {
      szb_close_form: handleClose,
    },
    [isOpen]
  );

  return (
    <Drawer open={isOpen} className="bg-white h-full">
      <DrawerContent
        position="bottom"
        size="auto"
        className="min-h-[440px] max-h-[440px] h-full md:max-h-[620px] md:min-h-[620px]"
        onClickOutside={handleClickOutside}
      >
        <SizeRecommendationForm resetRecommendation={handleResetRecommendation} onClose={handleClose} />
      </DrawerContent>
    </Drawer>
  );
}

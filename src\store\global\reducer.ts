import { GlobalState } from './types';
import { GenericStep, PocketVFRStep } from '@/types/pocket-virtual-fitting-room';

export const initialState: GlobalState = {
  sessionId: '',
  personaHash: null,
  errorCreatingProfile: false,
  previousSizeNotFound: false,
  currentStep: GenericStep.SELECT_CATEGORY,
  loadingRecommendation: false,
};

export enum GlobalActionTypes {
  GET_SESSION_INFO = 'GET_SESSION_INFO',
  SET_ERROR_PROFILE = 'SET_ERROR_PROFILE',
  SET_PREVIOUS_SIZE_NOT_FOUND = 'SET_PREVIOUS_SIZE_NOT_FOUND',
  SET_PERSONA_HASH = 'SET_PERSONA_HASH',
  SET_CURRENT_STEP = 'SET_CURRENT_STEP',
  SET_LOADING_RECOMMENDATION = 'SET_LOADING_RECOMMENDATION',
}

type SessionAction =
  | {
      kind: GlobalActionTypes.GET_SESSION_INFO;
      payload: {
        sessionId: string;
        personaHash: string;
      };
    }
  | { kind: GlobalActionTypes.SET_ERROR_PROFILE; payload: boolean | null }
  | { kind: GlobalActionTypes.SET_PREVIOUS_SIZE_NOT_FOUND; payload: boolean }
  | { kind: GlobalActionTypes.SET_PERSONA_HASH; payload: string | null }
  | { kind: GlobalActionTypes.SET_CURRENT_STEP; payload: PocketVFRStep }
  | { kind: GlobalActionTypes.SET_LOADING_RECOMMENDATION; payload: boolean };

export function sessionReducer(state: GlobalState, action: SessionAction): GlobalState {
  switch (action.kind) {
    case GlobalActionTypes.GET_SESSION_INFO:
      return {
        ...state,
        sessionId: action.payload.sessionId,
        personaHash: action.payload.personaHash,
      };
    case GlobalActionTypes.SET_ERROR_PROFILE:
      return {
        ...state,
        errorCreatingProfile: action.payload,
      };
    case GlobalActionTypes.SET_PREVIOUS_SIZE_NOT_FOUND:
      return {
        ...state,
        previousSizeNotFound: action.payload,
      };
    case GlobalActionTypes.SET_PERSONA_HASH:
      return {
        ...state,
        personaHash: action.payload,
      };
    case GlobalActionTypes.SET_CURRENT_STEP:
      return {
        ...state,
        currentStep: action.payload,
      };
    case GlobalActionTypes.SET_LOADING_RECOMMENDATION:
      return {
        ...state,
        loadingRecommendation: action.payload,
      };
    default:
      return state;
  }
}

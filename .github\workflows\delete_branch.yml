name: Delete feature branch after merge into release (if also in develop)

on:
  pull_request:
    types: [closed]
    branches:
      - 'release/**'

jobs:
  delete-feature-branch:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    steps:
      - name: Check if PR source branch was also merged into develop
        run: |
          BRANCH_NAME="${{ github.event.pull_request.head.ref }}"
          OWNER_REPO="${{ github.repository }}"

          echo "Checking if branch '$BRANCH_NAME' is merged into develop..."

          git clone --depth=1 https://github.com/$OWNER_REPO.git repo
          cd repo
          
          git fetch origin develop
          git fetch origin $BRANCH_NAME
          
          # Check if last commit from PR branch is in develop.
          
          git merge-base --is-ancestor origin/$BRANCH_NAME origin/develop && HAS_MERGED=true || HAS_MERGED=false

          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
          echo "HAS_MERGED=$HAS_MERGED" >> $GITHUB_ENV

      - name: Delete branch if also merged into develop
        if: env.HAS_MERGED == 'true'
        uses: peter-evans/delete-branch@v3
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          branch: ${{ github.event.pull_request.head.ref }}
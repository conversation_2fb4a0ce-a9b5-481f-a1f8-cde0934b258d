name: Run E2E Tests

on:
  pull_request:
    branches:
      - main
      - develop
  push:
    branches:
      - main
      - develop
      - release

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.13.1'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: Install Dependencies    
        run: pnpm install

      - name: Run unit tests
        run: pnpm test

      - name: Upload coverage report
        run: pnpm test:coverage
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: coverage

  cypress-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4.1.7

      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '22.13.1'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: Install Dependencies
        run: pnpm install

      - name: Cypress run
        uses: cypress-io/github-action@v6
        with:
          build: pnpm build
          start: pnpm preview:test

      - name: Upload screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-screenshots
          path: cypress/screenshots

      - name: Check coverage with NYC
        run: |
          minimum_coverage=60
          coverage=$(pnpm coverage)
          lines=$(echo "$coverage" | grep "Lines" | awk '{print $3}' | tr -d '%')
          stataments=$(echo "$coverage" | grep "Statements" | awk '{print $3}' | tr -d '%')
          functions=$(echo "$coverage" | grep "Functions" | awk '{print $3}' | tr -d '%')
          branches=$(echo "$coverage" | grep "Branches" | awk '{print $3}' | tr -d '%')

          average=$(awk "BEGIN {print int(($lines + $stataments + $functions + $branches) / 4)}")

          if awk "BEGIN {exit !($average < $minimum_coverage)}"; then
            echo "Coverage is below ${minimum_coverage}%. Current coverage: ${average}%"
            exit 1
          else
            echo "Coverage is sufficient: ${average}% "
          fi

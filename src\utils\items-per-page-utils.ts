import { DeviceType, useDevice } from '../hooks/use-device';
import { CarouselVariant } from '@/types/carousel';

export const SimilarItemsPerPageMap: Record<DeviceType, number> = {
  [DeviceType.Mobile]: 1,
  [DeviceType.SmallTablet]: 2,
  [DeviceType.Tablet]: 3,
  [DeviceType.Laptop]: 3,
  [DeviceType.Desktop]: 4,
};

export const ComplementaryItemsPerPageMap: Record<DeviceType, number> = {
  [DeviceType.Mobile]: 1,
  [DeviceType.SmallTablet]: 1,
  [DeviceType.Tablet]: 1,
  [DeviceType.Laptop]: 3,
  [DeviceType.Desktop]: 3,
};

/**
 * A hook that returns the number of items per page based on the current device
 * @param type The device type
 * @param itemsType The type of items (similar or complementary)
 * @returns The number of items per page
 */
export const useItemsPerPage = ({ itemsType = CarouselVariant.Similar }: { itemsType: CarouselVariant }) => {
  const deviceType = useDevice() as DeviceType;
  switch (itemsType) {
    case CarouselVariant.Similar:
      return SimilarItemsPerPageMap[deviceType];
    case CarouselVariant.Complementary:
      return ComplementaryItemsPerPageMap[deviceType];
  }
};

/**
 * @deprecated Use useItemsPerPage hook instead
 * Legacy function to get the number of items per page based on window width
 * This doesn't respond to window resize events automatically
 */
export const getItemsPerPageNumber = () => {
  const width = window.innerWidth;
  if (width < 640) return 1;
  if (width < 1280) return 2;
  return 3;
};

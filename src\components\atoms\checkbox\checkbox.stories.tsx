import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Checkbox } from './checkbox';

const meta = {
  title: 'Atoms/Checkbox',
  component: Checkbox,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    checked: {
      description: 'Whether the checkbox is checked',
      control: 'boolean',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    disabled: {
      description: 'Whether the checkbox is disabled',
      control: 'boolean',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    className: {
      description: 'Additional CSS classes to apply',
      control: 'text',
    },
  },
} satisfies Meta<typeof Checkbox>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    checked: false,
  },
};

export const Checked: Story = {
  args: {
    checked: true,
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
  },
};

export const DisabledChecked: Story = {
  args: {
    checked: true,
    disabled: true,
  },
};

export const Interactive: Story = {
  render: () => {
    const InteractiveCheckbox = () => {
      const [checked, setChecked] = useState(false);
      return (
        <div className="flex items-center gap-2">
          <Checkbox checked={checked} onChange={(e) => setChecked(e.target.checked)} id="interactive-checkbox" />
          <label htmlFor="interactive-checkbox" className="text-sm cursor-pointer">
            Click me to toggle
          </label>
        </div>
      );
    };
    return <InteractiveCheckbox />;
  },
};

export const AllStates: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <Checkbox id="unchecked" />
        <label htmlFor="unchecked">Unchecked</label>
      </div>
      <div className="flex items-center gap-4">
        <Checkbox id="checked" checked />
        <label htmlFor="checked">Checked</label>
      </div>
      <div className="flex items-center gap-4">
        <Checkbox id="disabled" disabled />
        <label htmlFor="disabled">Disabled</label>
      </div>
      <div className="flex items-center gap-4">
        <Checkbox id="disabled-checked" disabled checked />
        <label htmlFor="disabled-checked">Disabled & Checked</label>
      </div>
    </div>
  ),
};

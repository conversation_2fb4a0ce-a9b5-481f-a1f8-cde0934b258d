import type { <PERSON>a, StoryObj } from '@storybook/react';
import { CarouselNavigationButton } from './carousel-navigation-button';

const meta = {
  title: 'Molecules/CarouselNavigationButton',
  component: CarouselNavigationButton,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
          Botão de navegação do carrossel com suporte a direções esquerda/direita.
          
          ### Características
          - Feedback visual ao hover
          - Estado desabilitado
          - Ícones direcionais
          - Totalmente acessível
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    direction: {
      description: 'Direção do botão',
      control: 'radio',
      options: ['left', 'right'],
    },
    onClick: {
      description: 'Função chamada ao clicar no botão',
      control: false,
    },
    disabled: {
      description: 'Estado desabilitado do botão',
      control: 'boolean',
    },
    className: {
      description: 'Classes CSS adicionais',
      control: 'text',
    },
  },
} satisfies Meta<typeof CarouselNavigationButton>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Left: Story = {
  args: {
    direction: 'left',
    onClick: () => console.log('Previous clicked'),
    disabled: false,
  },
};

export const Right: Story = {
  args: {
    direction: 'right',
    onClick: () => console.log('Next clicked'),
    disabled: false,
  },
};

export const Disabled: Story = {
  args: {
    direction: 'left',
    onClick: () => console.log('Clicked'),
    disabled: true,
  },
};

import { PocketVFRStep } from '@/types/pocket-virtual-fitting-room';

export interface GlobalState {
  sessionId: string;
  personaHash: string | null;
  errorCreatingProfile: boolean | null;
  previousSizeNotFound: boolean;
  currentStep: PocketVFRStep;
  loadingRecommendation: boolean;
}

export interface GlobalActions {
  setErrorProfile: (value: boolean | null) => void;
  setPersonaHash: (value: string | null) => void;
  setPreviousSizeNotFound: (value: boolean) => void;
  setCurrentStep: (value: PocketVFRStep) => void;
  setLoadingRecommendation: (value: boolean) => void;
}

export interface GlobalContextData {
  state: GlobalState;
  actions: GlobalActions;
}

import { ClothType } from '@/types/carousel';
import { ComplementaryProduct } from '@/types/products';

export interface ContainerDimensions {
  container: HTMLDivElement;
  itemWidth: number;
  currentScroll: number;
  scrollableWidth: number;
  remainingScroll: number;
  itemTotalWidth: number;
}

export const calculateTotalPages = (totalItems: number, itemsPerPage: number): number => {
  return Math.ceil(totalItems / itemsPerPage);
};

export const sortComplementaryProductsByClothType = (products: ComplementaryProduct[]): ComplementaryProduct[] => {
  if (!products?.length) return [];

  const newComplementaryProducts = new Set<ComplementaryProduct>();

  const clothTypePreference = {
    first: [
      ClothType.TOP,
      ClothType.UNDERWEAR_TOP,
      ClothType.WETSUIT_TOP,
      ClothType.FULL_BODY,
      ClothType.UNDERWEAR_FULL_BODY,
      ClothType.WETSUIT_FULL_BODY,
    ],
    second: [ClothType.BOTTOM, ClothType.UNDERWEAR_BOTTOM, ClothType.WETSUIT_BOTTOM],
    third: [ClothType.SHOE_ACCESSORY],
  };

  Object.keys(clothTypePreference).forEach((key) => {
    const type = clothTypePreference[key as keyof typeof clothTypePreference];
    const filteredProducts = products.find((product) => type.includes(product.clothType));
    if (filteredProducts) {
      newComplementaryProducts.add(filteredProducts);
    }
  });

  return Array.from(newComplementaryProducts);
};

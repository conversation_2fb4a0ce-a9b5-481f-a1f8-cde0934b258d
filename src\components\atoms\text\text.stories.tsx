import type { Meta, StoryObj } from '@storybook/react';
import { Text } from './text';

const meta = {
  title: 'Atoms/Text',
  component: Text,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      description: 'The size of the text',
      options: ['xl', 'lg', 'md', 'sm', 'xs'],
      control: { type: 'select' },
      table: {
        defaultValue: { summary: 'md' },
      },
    },
    weight: {
      description: 'The weight/boldness of the text',
      options: ['regular', 'medium', 'semibold'],
      control: { type: 'select' },
      table: {
        defaultValue: { summary: 'regular' },
      },
    },
    color: {
      description: 'The color of the text',
      options: ['white', 'black', 'gray'],
      control: { type: 'select' },
      table: {
        defaultValue: { summary: 'black' },
      },
    },
    as: {
      description: 'The HTML element to render',
      options: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'div'],
      control: { type: 'select' },
      table: {
        defaultValue: { summary: 'p' },
      },
    },
    children: {
      description: 'The content to display',
      control: 'text',
    },
    className: {
      description: 'Additional CSS classes to apply',
      control: 'text',
    },
  },
} satisfies Meta<typeof Text>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'This is a default text',
    size: 'md',
  },
};

export const Sizes: Story = {
  args: {
    size: 'md',
    children: 'Text Example',
  },
  render: () => (
    <div className="space-y-2">
      <Text size="xl">Extra Large Text (xl)</Text>
      <Text size="lg">Large Text (lg)</Text>
      <Text size="md">Medium Text (md)</Text>
      <Text size="sm">Small Text (sm)</Text>
      <Text size="xs">Extra Small Text (xs)</Text>
    </div>
  ),
};

export const Weights: Story = {
  args: {
    size: 'md',
    children: 'Text Example',
  },
  render: () => (
    <div className="space-y-2">
      <Text size="md" weight="regular">
        Regular Weight
      </Text>
      <Text size="md" weight="medium">
        Medium Weight
      </Text>
      <Text size="md" weight="semibold">
        Semibold Weight
      </Text>
    </div>
  ),
};

export const Colors: Story = {
  args: {
    size: 'md',
    children: 'Text Example',
  },
  render: () => (
    <div className="space-y-2">
      <Text size="md" color="black">
        Black Text
      </Text>
      <div className="bg-gray-900 p-2">
        <Text size="md" color="white">
          White Text
        </Text>
      </div>
      <Text size="md" color="gray">
        Gray Text
      </Text>
    </div>
  ),
};

export const AsElements: Story = {
  args: {
    size: 'md',
    children: 'Text Example',
  },
  render: () => (
    <div className="space-y-2">
      <Text size="xl" as="h1" weight="semibold">
        Heading 1
      </Text>
      <Text size="lg" as="h2" weight="semibold">
        Heading 2
      </Text>
      <Text size="md" as="p">
        Paragraph
      </Text>
      <Text size="sm" as="span">
        Span Text
      </Text>
    </div>
  ),
};

export const Composition: Story = {
  args: {
    size: 'md',
    children: 'Text Example',
  },
  render: () => (
    <div className="space-y-4">
      <Text size="xl" weight="semibold" color="black" as="h1">
        Main Heading
      </Text>
      <Text size="md" color="gray">
        This is a paragraph with medium size and gray color. It demonstrates how the Text component can be used in a
        real-world context.
      </Text>
      <Text size="sm" weight="medium" color="black">
        A smaller text with medium weight
      </Text>
    </div>
  ),
};

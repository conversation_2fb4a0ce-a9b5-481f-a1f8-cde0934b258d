import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi } from 'vitest';
import { Button } from '../../../components/atoms';
import { GraduationCapIcon } from '../../../components/icons';

describe('Button', () => {
  it('should render with base styles', () => {
    render(<Button>Base styles</Button>);
    expect(screen.getByRole('button')).toHaveClass(
      'rounded-lg px-4 py-3 font-medium cursor-pointer inline-flex items-center justify-center gap-3'
    );
  });

  it('should renders correctly with default props', () => {
    render(<Button>Default</Button>);
    const button = screen.getByRole('button', { name: /default/i });
    expect(button).toBeInTheDocument();
    expect(screen.getByRole('button'));
  });

  it('should render all different variants correctly', () => {
    const { rerender } = render(<Button variant="primary">Primary</Button>);
    const primaryButton = screen.getByRole('button', { name: /Primary/i });
    expect(primaryButton).toHaveClass('bg-[#272727] text-[#FFFFFF]');
    expect(primaryButton).toHaveClass('border border-[#272727]');
    expect(primaryButton).toHaveClass('hover:border-[#AEAEAE]/90 hover:bg-[#AEAEAE]/90');
    expect(primaryButton).not.toHaveClass('w-full');

    rerender(<Button variant="secondary">Secondary</Button>);
    const secondaryButton = screen.getByRole('button', { name: /Secondary/i });
    expect(secondaryButton).toHaveClass('bg-[#FFFFFF] text-[#262626]');
    expect(secondaryButton).toHaveClass('border border-[#272727]');
    expect(secondaryButton).toHaveClass('hover:border-[#D6D6D6]/90');
    expect(secondaryButton).not.toHaveClass('w-full');

    rerender(<Button variant="blank">Blank</Button>);
    const blankButton = screen.getByRole('button', { name: /Blank/i });
    expect(blankButton).not.toHaveClass('w-full');
    expect(blankButton).not.toHaveClass('border');
    expect(blankButton).not.toHaveClass('bg-[#272727]');
  });

  it('should handles click events', async () => {
    const handleClick = vi.fn();
    const user = userEvent.setup();

    render(<Button onClick={handleClick}>Click me</Button>);

    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should render with icon correctly', () => {
    render(<Button icon={<GraduationCapIcon />}>With Icon</Button>);
    const button = screen.getByRole('button');

    expect(button).toHaveClass('gap-3');
    expect(button.querySelector('svg')).toBeInTheDocument();

    expect(button.children[0]).toContainHTML('svg');
    expect(button.childNodes[1]).toHaveTextContent('With Icon');
  });

  it('should render icon-only button', () => {
    render(<Button icon={<GraduationCapIcon />} />);
    const button = screen.getByRole('button');

    expect(button.children).toHaveLength(1);
    expect(button.querySelector('svg')).toBeInTheDocument();
    expect(button).toHaveTextContent('');
  });

  it('should apply full width style when fullWidth prop is true', () => {
    render(<Button fullWidth>Full Width</Button>);
    expect(screen.getByRole('button')).toHaveClass('w-full');
  });

  it('should accept and apply custom className', () => {
    render(<Button className="custom-class">Custom</Button>);
    expect(screen.getByRole('button')).toHaveClass('custom-class');
  });
});

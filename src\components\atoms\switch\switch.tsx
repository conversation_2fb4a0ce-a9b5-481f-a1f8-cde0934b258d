import { forwardRef, useCallback, useId, useState } from 'react';
import { rootBaseStyles, rootSizeStyles, thumbBaseStyles, thumbSizeStyles, variantStyles } from './switch.styles';
import { SwitchProps } from './switch.types';
import { cn } from '../../../utils/class-utils';

export const Switch = forwardRef<HTMLButtonElement, SwitchProps>(
  (
    {
      className,
      variant = 'default',
      size = 'sm',
      checked,
      defaultChecked,
      onCheckedChange,
      disabled,
      label,
      description,
      'aria-label': ariaLabel,
      ...props
    },
    ref
  ) => {
    const [isChecked, setIsChecked] = useState(defaultChecked ?? false);
    const isControlled = checked !== undefined;
    const switchChecked = isControlled ? checked : isChecked;

    const handleClick = useCallback(() => {
      if (!isControlled) {
        setIsChecked(!switchChecked);
      }
      onCheckedChange?.(!switchChecked);
    }, [isControlled, onCheckedChange, switchChecked]);

    const id = useId();
    const labelId = `${id}-label`;
    const descriptionId = `${id}-description`;

    return (
      <div className="flex items-center gap-3">
        <button
          type="button"
          role="switch"
          id={id}
          aria-checked={switchChecked}
          aria-label={!label ? ariaLabel : ''}
          aria-labelledby={label ? labelId : ''}
          aria-describedby={description ? descriptionId : ''}
          data-state={switchChecked ? 'checked' : 'unchecked'}
          disabled={disabled}
          ref={ref}
          onClick={handleClick}
          className={cn(rootBaseStyles, rootSizeStyles[size], variantStyles[variant], className)}
          {...props}
        >
          <span
            className={cn(thumbBaseStyles, thumbSizeStyles[size])}
            data-state={switchChecked ? 'checked' : 'unchecked'}
          />
        </button>
        {(label || description) && (
          <div className="flex flex-col gap-0.5">
            {label && (
              <label
                htmlFor={id}
                id={labelId}
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                {label}
              </label>
            )}
            {description && (
              <p id={descriptionId} className="text-sm text-muted-foreground">
                {description}
              </p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Switch.displayName = 'Switch';

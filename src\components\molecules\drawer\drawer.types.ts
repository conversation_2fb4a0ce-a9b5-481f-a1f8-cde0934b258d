import React from 'react';

export type DrawerPosition = 'left' | 'right' | 'top' | 'bottom';
export type DrawerSize = 'sm' | 'md' | 'lg' | 'xl' | 'full' | 'auto';

export interface DrawerContextProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export interface DrawerProps {
  children: React.ReactNode;
  className?: string;
  defaultOpen?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export interface DrawerTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  asChild?: boolean;
}

export interface DrawerOverlayProps extends React.HTMLAttributes<HTMLDivElement> {
  closeOnClick?: boolean;
}

export interface DrawerContentProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'title'> {
  children: React.ReactNode;
  position?: DrawerPosition;
  size?: DrawerSize;
  showCloseButton?: boolean;
  closeOnOutsideClick?: boolean;
  withHeader?: boolean;
  title?: React.ReactNode;
  withFooter?: boolean;
  footer?: React.ReactNode;
  onClickOutside?: () => void;
}

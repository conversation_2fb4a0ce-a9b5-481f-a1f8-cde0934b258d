import { useEffect, useRef, useState } from 'react';
import { cn } from '../../../utils/class-utils';
import { ArrowIcon } from '../../icons';
import { Text } from '../text';

interface SelectProps {
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
  options: string[];
}

const SELECT_STYLES = {
  CONTAINER: 'relative w-full min-w-[100px] max-w-[220px]',
  BUTTON:
    'flex items-center justify-between gap-2 w-full p-1 text-gray-500 bg-white border-b border-b-[#D6D6D6] focus:outline-none',
  BUTTON_TEXT: 'flex flex-row items-center justify-center gap-2',
  ARROW_OPEN: '-rotate-90 transition-all',
  ARROW_CLOSED: 'rotate-90 transition-all',
  DROPDOWN: 'absolute z-10 w-full mt-1 bg-white border border-[#D6D6D6] rounded-md shadow-sm',
  OPTION_LIST: 'py-1',
  OPTION: 'px-3 py-2 cursor-pointer hover:bg-gray-50',
  SELECTED_OPTION: 'bg-gray-100',
};

export function Select({ placeholder, value, onChange, options }: SelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectOption] = useState(value);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleSelect = (size: string) => {
    setSelectOption(size);
    onChange?.(size);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className={SELECT_STYLES.CONTAINER} ref={dropdownRef}>
      <button type="button" className={SELECT_STYLES.BUTTON} onClick={toggleDropdown}>
        <Text as="span" size="md" color="gray" className={SELECT_STYLES.BUTTON_TEXT}>
          {selectedOption || placeholder}
        </Text>
        <ArrowIcon className={isOpen ? SELECT_STYLES.ARROW_OPEN : SELECT_STYLES.ARROW_CLOSED} size={16} />
      </button>

      {isOpen && (
        <div className={SELECT_STYLES.DROPDOWN}>
          <ul className={SELECT_STYLES.OPTION_LIST}>
            {options.map((opt) => (
              <li
                key={opt}
                className={cn(SELECT_STYLES.OPTION, {
                  [SELECT_STYLES.SELECTED_OPTION]: selectedOption === opt,
                })}
                onClick={() => handleSelect(opt)}
              >
                {opt}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}

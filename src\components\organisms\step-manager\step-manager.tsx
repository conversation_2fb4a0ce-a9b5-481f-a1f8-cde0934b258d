import { BodyMeasuresStep } from './steps/body-measures-step';
import { BodyShapeStep } from './steps/body-shape-step';
import { CategoryStep } from './steps/category-step';
import { ClothingStep, GenericStep } from '@/types/pocket-virtual-fitting-room';
import { useGlobalContext } from '@/store/global';
import { DeviceType, useDevice } from '@/hooks/use-device';

export const StepManager = () => {
  const {
    state: { currentStep },
  } = useGlobalContext();
  const device = useDevice() as DeviceType;
  const isSmallerDevices = [DeviceType.SmallTablet, DeviceType.Mobile].includes(device);

  return (
    <>
      {currentStep === GenericStep.SELECT_CATEGORY && <CategoryStep isSmallerDevices={isSmallerDevices} />}
      {currentStep === ClothingStep.BODY_MEASURES && <BodyMeasuresStep isSmallerDevices={isSmallerDevices} />}
      {currentStep === ClothingStep.BODY_SHAPE && <BodyShapeStep isSmallerDevices={isSmallerDevices} />}
    </>
  );
};

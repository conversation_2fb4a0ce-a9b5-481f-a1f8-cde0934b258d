import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Skeleton } from './skeleton';

const meta = {
  title: 'Atoms/Skeleton',
  component: Skeleton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    className: {
      description: 'Additional CSS classes to apply',
      control: 'text',
    },
  },
} satisfies Meta<typeof Skeleton>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    className: 'w-32 h-32',
  },
};

export const Text: Story = {
  args: {
    className: 'h-4 w-48',
  },
};

export const Circle: Story = {
  args: {
    className: 'h-12 w-12 rounded-full',
  },
};

export const Rectangle: Story = {
  args: {
    className: 'h-48 w-32 rounded-lg',
  },
};

export const ProductCardSkeleton: Story = {
  render: () => (
    <div className="space-y-2">
      <Skeleton className="h-[328px] rounded-lg" />
      <Skeleton className="h-6 w-3/4" />
      <Skeleton className="h-6 w-1/4" />
    </div>
  ),
};

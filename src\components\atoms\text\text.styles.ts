export type TextSize = 'xl' | 'lg' | 'md' | 'sm' | 'xs';
export type TextWeight = 'regular' | 'medium' | 'semibold';
export type TextColor = 'white' | 'black' | 'gray';
export type TypographyElement = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div' | 'label';

export const TEXT_SIZES: Record<TextSize, string> = {
  xl: 'text-[16px] lg:text-[20px]',
  lg: 'text-[12px] lg:text-[18px]',
  md: 'text-[14px] lg:text-[16px]',
  sm: 'text-[12px] lg:text-[14px]',
  xs: 'text-[10px] lg:text-[12px]',
};

export const TEXT_WEIGHTS: Record<TextWeight, string> = {
  regular: 'font-normal',
  medium: 'font-medium',
  semibold: 'font-semibold',
};

export const TEXT_COLORS: Record<string, string> = {
  white: 'text-[#FFFFFF]',
  black: 'text-[#272727]',
  gray: 'text-[#5D5D5D]',
};

import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Text, Tooltip } from '@/components/atoms';
import { GraduationCapIcon } from '@/components/icons';
import { TextFilter } from '@/components/molecules';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/molecules/popover';
import { SizeRecommendationForm } from '@/components/organisms';
import { DeviceType, useDevice } from '@/hooks/use-device';
import { postMessageToParent } from '@/hooks/use-post-message';
import { useGlobalContext } from '@/store/global';
import { cn } from '@/utils/class-utils';

const SizeGuideStyles = {
  container: {
    base: 'flex flex-row justify-between items-center',
    withoutPersona: 'justify-end',
  },
  textFilter: {
    text: 'px-2 py-1 whitespace-nowrap',
    button: 'p-1',
  },
  popover: 'relative flex justify-end w-full',
  trigger: {
    base: 'rounded-lg border border-[#EBEBEB] max-w-full p-2 w-full',
  },
  tooltip: {
    mobile: 'w-full',
  },
  withoutProfile: 'size-guide-trigger-width',
  buttonText: 'whitespace-nowrap',
};

export const SizeGuideSection = () => {
  const [popoverOpen, setPopoverOpen] = useState(false);
  const { t } = useTranslation();

  const {
    state: { sessionId, personaHash, errorCreatingProfile, previousSizeNotFound },
  } = useGlobalContext();
  const isSmallTablet = useDevice(DeviceType.SmallTablet) as boolean;
  const isMobile = useDevice(DeviceType.Mobile) as boolean;

  let hasRecommendation = false;

  const handleOpenChange = (open: boolean) => setPopoverOpen(open);

  const resetPersonaHash = () => {
    postMessageToParent({ id: 'szb_reset_persona_hash' });
  };

  if (sessionId && personaHash) hasRecommendation = true;

  const handleToggle = () => {
    if (isSmallTablet || isMobile) {
      postMessageToParent({ id: 'szb_toggle_drawer' });
    }
    if (!isSmallTablet && !isMobile) {
      setPopoverOpen(!popoverOpen);
    }
  };

  const showProductsWithYourSize = hasRecommendation && personaHash;

  return (
    <div className={cn(SizeGuideStyles.container.base, !personaHash && SizeGuideStyles.container.withoutPersona)}>
      {personaHash && showProductsWithYourSize && (
        <TextFilter
          text={t('fashion_hint.products_with_your_size')}
          onClose={resetPersonaHash}
          className={SizeGuideStyles.textFilter.text}
        />
      )}
      <Popover className={SizeGuideStyles.popover} open={popoverOpen} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          <Tooltip
            content={t('pocket_vfr.error.size_not_found')}
            showAndFade
            delay={7000}
            placement="bottom"
            contentClassName="w-full"
            rootClassName={cn(
              { [SizeGuideStyles.tooltip.mobile]: isMobile },
              { [SizeGuideStyles.withoutProfile]: !personaHash }
            )}
            disabled={!(!errorCreatingProfile && !personaHash && previousSizeNotFound)}
          >
            <Button
              id="size-guide-trigger"
              variant="blank"
              onClick={handleToggle}
              className={cn(SizeGuideStyles.trigger.base, 'fh-size__guide__button', {
                [SizeGuideStyles.withoutProfile]: !personaHash,
              })}
            >
              <GraduationCapIcon className="fh-size__guide__button__icon" />
              {!personaHash && !showProductsWithYourSize && (
                <Text as="span" size="sm" weight="medium" className={'text-nowrap fh-size__guide__button__text'}>
                  {t('fashion_hint.virtual_fitting_room')}
                </Text>
              )}
            </Button>
          </Tooltip>
        </PopoverTrigger>
        {!isSmallTablet && !isMobile && (
          <PopoverContent
            position="bottom"
            className={cn('rounded-[8px] form-width', {
              'w-[19rem] min-w-full': isMobile,
            })}
            align={'end'}
          >
            <SizeRecommendationForm resetRecommendation={resetPersonaHash} />
          </PopoverContent>
        )}
      </Popover>
    </div>
  );
};

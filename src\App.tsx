import { useEffect } from 'react';
import { useQueryParams } from './hooks';
import { Router } from './router';
import './styles/App.css';
import { injectTenantStyles } from './utils/injectTenantStyles';

function App() {
  const { raw: params } = useQueryParams();
  const { tenantId } = params;

  useEffect(() => {
    if (tenantId) {
      injectTenantStyles(tenantId);
    }
  }, [tenantId]);

  useEffect(() => {
    const el = document.querySelector<HTMLElement>('.fashion-hint-content');
    if (!el) return;

    const sendHeight = () => {
      const height = Math.max(el.scrollHeight, el.getBoundingClientRect().height);
      if (height === 0) return;
      window.parent.postMessage(
        {
          id: 'szb_content_height',
          height,
          context: window.location.pathname.includes('similar') ? 'similar' : 'complementary',
        },
        '*'
      );
    };

    sendHeight();

    const observer = new ResizeObserver(() => {
      sendHeight();
    });
    observer.observe(el);

    return () => observer.disconnect();
  }, []);

  return <Router />;
}

export default App;

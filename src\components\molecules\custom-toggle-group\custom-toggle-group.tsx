import { ToggleGroup, ToggleGroupItem } from '../../atoms/toggle-group/toggle-group';

interface ToggleOption {
  value: string;
  label: string;
  className?: string;
}

interface CustomToggleGroupProps {
  options: ToggleOption[];
  value: string;
  onChange: (value: string) => void;
  groupClassName?: string;
  itemClassName?: (option: ToggleOption, selected: boolean) => string;
}

export const CustomToggleGroup = ({
  options,
  value,
  onChange,
  groupClassName,
  itemClassName,
}: CustomToggleGroupProps) => (
  <ToggleGroup
    type="single"
    className={groupClassName}
    value={value}
    onValueChange={(val) => {
      if (typeof val === 'string') {
        onChange(val);
      }
    }}
  >
    {options.map((option) => (
      <ToggleGroupItem
        key={option.value}
        value={option.value}
        className={itemClassName ? itemClassName(option, value === option.value) : option.className}
      >
        {option.label}
      </ToggleGroupItem>
    ))}
  </ToggleGroup>
);

export enum ProductCardType {
  BaseProduct = 'baseProduct',
  FirstProduct = 'first',
  SecondaryProduct = 'second',
}

export enum ProductCardAlignment {
  OneColumn = 'oneColumn',
  TwoColumns = 'twoColumns',
}

export enum CatalogClothType {
  TOP = 'TOP',
  BOTTOM = 'BOTTOM',
  SHOE_ACCESSORY = 'SHOE_ACCESSORY',
  FULL_BODY = 'FULL_BODY',
  UNDERWEAR_FULL_BODY = 'UNDERWEAR_FULL_BODY',
  UNDERWEAR_BOTTOM = 'UNDERWEAR_BOTTOM',
  UNDERWEAR_TOP = 'UNDERWEAR_TOP',
  WETSUIT_FULL_BODY = 'WETSUIT_FULL_BODY',
  WETSUIT_BOTTOM = 'WETSUIT_BOTTOM',
  WETSUIT_TOP = 'WETSUIT_TOP',
}

export const ClothType = {
  TOP: CatalogClothType.TOP,
  BOTTOM: CatalogClothType.BOTTOM,
  SHOE_ACCESSORY: CatalogClothType.SHOE_ACCESSORY,
  FULL_BODY: CatalogClothType.FULL_BODY,
  UNDERWEAR_FULL_BODY: CatalogClothType.UNDERWEAR_FULL_BODY,
  UNDERWEAR_BOTTOM: CatalogClothType.UNDERWEAR_BOTTOM,
  UNDERWEAR_TOP: CatalogClothType.UNDERWEAR_TOP,
  WETSUIT_FULL_BODY: CatalogClothType.WETSUIT_FULL_BODY,
  WETSUIT_BOTTOM: CatalogClothType.WETSUIT_BOTTOM,
  WETSUIT_TOP: CatalogClothType.WETSUIT_TOP,
};

export enum CarouselVariant {
  Complementary = 'complementary',
  Similar = 'similar',
}

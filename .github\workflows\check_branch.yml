name: 'Check Branch'

on:
  pull_request:
    branches:
      - main

jobs:
  check_branch:
    runs-on: ubuntu-latest
    steps:
      - name: Validate source branch name
        run: |
          echo "Validating branch '${{ github.head_ref }}' -> '${{ github.base_ref }}'..."
          if [[ "${{ github.base_ref }}" == "main" && ! "${{ github.head_ref }}" =~ ^release\/.+$ ]]; then
            echo "❌ ERROR: Only branches matching 'release/*' can be merged into main."
            exit 1
          else
            echo "✅ Branch name is valid."
          fi

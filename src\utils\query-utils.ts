import { QueryParams } from '../hooks/use-query-params';
import { SimilarProductsParams } from '../types/products';

export function getQueryParams(): Record<string, string> {
  const queryParameters: Record<string, string> = {};

  const searchParams = new URLSearchParams(window.location.search);
  searchParams.forEach((value, key) => {
    try {
      queryParameters[key] = decodeURIComponent(String(value));
    } catch {
      queryParameters[key] = String(value);
    }
  });

  return queryParameters;
}

export function parseQueryParams(params: QueryParams): SimilarProductsParams {
  return {
    tenantId: Number(params.tenantId),
    sid: params.sid,
    collectionName: params.collectionName,
    permalink: params.permalink,
    page: Number(params.page),
    perPage: Number(params.perPage),
    sizeSystem: params.sizeSystem,
    currency: params.currency,
    locale: params.lang,
    similarityThreshold: Number(params.similarityThreshold),
  };
}

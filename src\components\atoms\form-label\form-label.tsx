import { FC, ReactNode } from 'react';
import { classes, cn } from '../../../utils/class-utils';

export interface FormLabelProps {
  /** The content of the label */
  children: ReactNode;
  /** Additional CSS classes */
  className?: string;
}

const baseStyles = classes([
  'text-gray-800',
  'text-center',
  'font-sans',
  'text-sm',
  'font-normal',
  'sm:mb-[6px] lg:mb-[10px] mb-[10px]',
]);

export const FormLabel: FC<FormLabelProps> = ({ children, className }) => {
  return (
    <label htmlFor={children?.toString()} className={cn(baseStyles, className)}>
      {children}
    </label>
  );
};

import { Carousel } from '@/components/organisms';
import { useComplementaryProducts } from '@/hooks/use-complementary-products';
import { useGlobalContext } from '@/store/global';
import { CarouselVariant } from '@/types/carousel';
import { useItemsPerPage } from '@/utils/items-per-page-utils';

export function ComplementarProductsPage() {
  const { products, isLoading } = useComplementaryProducts();
  const itemsPerPage = useItemsPerPage({ itemsType: CarouselVariant.Complementary });
  const {
    state: { personaHash },
  } = useGlobalContext();

  return (
    <Carousel
      isLoading={isLoading}
      products={products}
      isFetchingMore={false}
      itemsPerPage={itemsPerPage}
      type={CarouselVariant.Complementary}
      className={'fh-complementary'}
      key={personaHash}
    />
  );
}

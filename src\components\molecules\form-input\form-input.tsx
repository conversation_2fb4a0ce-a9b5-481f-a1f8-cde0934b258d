import { ChangeEvent, useCallback, useMemo } from 'react';
import {
  containerStyles,
  helperTextStyles,
  inputContainerStyles,
  primaryInputImperialHeightStyles,
  primaryInputStyles,
  secondaryInputStyles,
} from './form-input.styles';
import { cn } from '../../../utils/class-utils';
import { FormLabel, Input, Text } from '../../atoms';
import { UnitToggle } from '../unit-toggle';
import { MEASUREMENT_LIMITS } from '@/constants/pocket-virtual-fitting-room';

interface FormInputProps {
  label: string;
  helperText?: string;
  type: 'height' | 'weight' | 'age';
  defaultValue: string;
  secondaryValue?: string;
  isMetric?: boolean;
  handleChange: (e: ChangeEvent<HTMLInputElement>) => void;
  onToggleChange?: () => void;
  metricUnit?: 'cm' | 'kg';
  imperialUnit?: 'in' | 'lb';
  className?: string;
}

export const FormInput = ({
  label,
  helperText,
  defaultValue,
  secondaryValue,
  isMetric = true,
  handleChange,
  onToggleChange,
  metricUnit,
  imperialUnit,
  type,
  className,
}: FormInputProps) => {
  const limits = useMemo(() => {
    if (type === 'age') return MEASUREMENT_LIMITS.age;
    if (type === 'height') {
      return isMetric ? MEASUREMENT_LIMITS.height.cm : MEASUREMENT_LIMITS.height.feet;
    }
    return isMetric ? MEASUREMENT_LIMITS.weight.kg : MEASUREMENT_LIMITS.weight.lb;
  }, [type, isMetric]);

  const clamp = (num: number) => Math.max(Math.min(num, limits.max), limits.min);

  const onPrimaryChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const raw = e.target.value;
      let num = raw === '' ? NaN : Number(raw);
      if (!isNaN(num)) {
        num = clamp(num);
        e.target.value = num.toString();
      }
      handleChange(e);
    },
    [clamp, handleChange]
  );

  const onSecondaryChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const raw = e.target.value;
      let num = raw === '' ? NaN : Number(raw);
      if (!isNaN(num)) {
        const inchLimits = MEASUREMENT_LIMITS.height.inches;
        num = Math.max(Math.min(num, inchLimits.max), inchLimits.min);
        e.target.value = num.toString();
      }
      handleChange(e);
    },
    [handleChange]
  );

  const onOnlyDigitsKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    const allowed = ['Backspace', 'Tab', 'ArrowLeft', 'ArrowRight', 'Delete'];
    if (!/[0-9]/.test(e.key) && !allowed.includes(e.key)) {
      e.preventDefault();
    }
  }, []);

  const onOnlyDigitsPaste = useCallback((e: React.ClipboardEvent<HTMLInputElement>) => {
    const pasted = e.clipboardData.getData('text');
    if (!/^[0-9]+$/.test(pasted)) {
      e.preventDefault();
    }
  }, []);

  const inputName = (key: number) => {
    if (type !== 'height' || isMetric) return type;
    return key === 1 ? 'heightFt' : 'heightIn';
  };

  return (
    <div className={cn(containerStyles, className)}>
      <FormLabel className="fh-form__input__label">{label}</FormLabel>
      <div className={inputContainerStyles}>
        <Input
          type="number"
          name={inputName(1)}
          value={defaultValue}
          onChange={onPrimaryChange}
          className={cn('fh-form__input', primaryInputStyles, {
            [primaryInputImperialHeightStyles]: type === 'height' && !isMetric,
          })}
          pattern="[0-9]*"
          onKeyDown={onOnlyDigitsKeyDown}
          onPaste={onOnlyDigitsPaste}
          min={limits.min}
          max={limits.max}
        />

        {!isMetric && type === 'height' && (
          <Input
            type="number"
            name={inputName(2)}
            value={secondaryValue}
            onChange={onSecondaryChange}
            className={cn(secondaryInputStyles, 'fh-form__input')}
            pattern="[0-9]*"
            onKeyDown={onOnlyDigitsKeyDown}
            onPaste={onOnlyDigitsPaste}
            min={MEASUREMENT_LIMITS.height.inches.min}
            max={MEASUREMENT_LIMITS.height.inches.max}
          />
        )}

        {metricUnit && imperialUnit && onToggleChange && (
          <UnitToggle
            isMetric={isMetric}
            onToggle={onToggleChange}
            metricUnit={metricUnit}
            imperialUnit={imperialUnit}
            className="w-8"
          />
        )}

        {helperText && (
          <Text as="span" size="sm" className={cn(helperTextStyles, 'fh-form__input__helper__text')}>
            {helperText.toLowerCase()}
          </Text>
        )}
      </div>
    </div>
  );
};

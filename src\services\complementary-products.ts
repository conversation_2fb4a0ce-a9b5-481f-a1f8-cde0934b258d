import { sizebayClient } from '../config/sizebay-client';
import { ComplementaryProducts, ComplementaryProductsParams } from '../types/products';

export async function getComplementaryProducts(
  params: ComplementaryProductsParams
): Promise<ComplementaryProducts | null> {
  try {
    const response: ComplementaryProducts = await sizebayClient.getComplementaryProducts({
      collectionName: params.collectionName,
      sid: params.sid,
      tenantId: params.tenantId,
      permalink: params.permalink,
      filterByWhatFitsMe: params.filterByWhatFitsMe,
      personaHash: params.personaHash,
      sizeSystem: params.sizeSystem ?? 'en',
      similarityThreshold: params.similarityThreshold,
      currency: params.currency,
      locale: params.locale,
    });

    if (response?.invalidPersonaHash) {
      window.parent.postMessage({ id: 'szb_reset_persona_hash' }, '*');
    }

    if (response.complementary.length === 0) {
      window.parent.postMessage({ id: 'szb_recommendation_not_found', type: 'complementary' }, '*');
      return null;
    }

    return {
      baseProduct: response?.baseProduct,
      complementary: response?.complementary,
      invalidPersonaHash: response?.invalidPersonaHash,
    };
  } catch (error) {
    const statusCode = (error as { statusCode?: number }).statusCode;

    if (statusCode === 404) {
      window.parent.postMessage({ id: 'szb_recommendation_not_found', type: 'complementary' }, '*');
    }

    return null;
  }
}

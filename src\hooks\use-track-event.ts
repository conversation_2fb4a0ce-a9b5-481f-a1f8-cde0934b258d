import { QueryParams, useQueryParams } from './use-query-params';
import { TrackEvents } from '@/constants/tracking-events';
import { trackEvent } from '@/services/track';
import { useGlobalContext } from '@/store/global';
import { ComplementaryProduct, SimilarProduct } from '@/types/products';
import { TrackEventParams, TrackEventPayload } from '@/types/track-types';

interface TrackPropertyMappers {
  [key: string]: (params: TrackPropertyMapperParams) => Record<string, unknown>;
}

interface TrackPropertyMapperParams {
  queryParams?: QueryParams;
  pageNum?: number;
  recommendationSize?: string;
  personaHash?: string;
  similarProduct?: SimilarProduct;
  baseProduct?: ComplementaryProduct;
  first?: ComplementaryProduct;
  secondary?: ComplementaryProduct;
  id?: string;
}

const trackPropertyMappers: TrackPropertyMappers = {
  [TrackEvents.SIMILARITY_RECOMMENDATION_CLICK]: ({
    queryParams,
    pageNum,
    recommendationSize,
    id,
    similarProduct,
    personaHash,
  }) => {
    const { collectionName, permalink } = queryParams!;
    const properties: Record<string, unknown> = {
      page: pageNum,
      ...similarProduct,
      personaHash,
      originalPermalink: permalink,
      collectionName,
      recommendation_data: {
        click: id,
      },
    };

    if (recommendationSize) {
      properties.recommendationSize = recommendationSize;
    }

    return properties;
  },
  [TrackEvents.COMPLEMENTARY_RECOMMENDATION_CLICK]: ({
    recommendationSize,
    baseProduct,
    first,
    secondary,
    id,
    personaHash,
  }) => {
    const properties: Record<string, unknown> = {
      personaHash,
      baseProduct,
      first,
      secondary,
      recommendation_data: {
        click: id,
      },
    };

    if (recommendationSize) {
      properties.recommendationSize = recommendationSize;
    }

    return properties;
  },
};

function getEventProperties(eventName: TrackEvents, params: TrackPropertyMapperParams): Record<string, unknown> {
  const mapper = trackPropertyMappers[eventName];
  if (!mapper) {
    return {};
  }

  return mapper(params);
}

interface GenerateTrackPayloadParams {
  eventName: TrackEvents;
  product: TrackEventParams['product'];
  recommendationSize?: string;
  personaHash?: string;
  queryParams: QueryParams;
  pageNum: number;
  sid: string;
  baseProduct?: ComplementaryProduct;
  first?: ComplementaryProduct;
  secondary?: ComplementaryProduct;
}

function generateTrackPayload(params: GenerateTrackPayloadParams): TrackEventPayload {
  const { eventName, product, recommendationSize, queryParams, pageNum, sid, personaHash } = params;
  const { tenantId } = queryParams;
  const { id } = product;
  const propertiesParams = { queryParams, pageNum, recommendationSize, id: id };

  if (personaHash) {
    Object.assign(propertiesParams, { personaHash });
  }

  if (eventName === TrackEvents.COMPLEMENTARY_RECOMMENDATION_CLICK) {
    Object.assign(propertiesParams, {
      baseProduct: params.baseProduct,
      first: params.first,
      secondary: params.secondary,
    });
  }
  if (eventName === TrackEvents.SIMILARITY_RECOMMENDATION_CLICK) {
    Object.assign(propertiesParams, { similarProduct: product });
  }

  const properties = getEventProperties(eventName, propertiesParams);

  return {
    eventName,
    sid,
    tenantId: Number(tenantId),
    permalink: product.link,
    properties: properties,
  };
}

export function useTrackEvent() {
  const { raw: queryParams } = useQueryParams();
  const {
    state: { sessionId, personaHash },
  } = useGlobalContext();

  const callTrackEvent = async (trackParams: TrackEventParams) => {
    const payload = generateTrackPayload({
      eventName: trackParams.eventName,
      product: trackParams.product,
      recommendationSize: trackParams.recommendationSize,
      baseProduct: trackParams.baseProduct,
      first: trackParams.first,
      secondary: trackParams.second,
      pageNum: 'pageNum' in trackParams.product ? (trackParams.product.pageNum ?? 1) : 1,
      sid: sessionId || '',
      personaHash: personaHash || '',
      queryParams,
    });

    await trackEvent(payload);
  };

  return {
    callTrackEvent,
  };
}

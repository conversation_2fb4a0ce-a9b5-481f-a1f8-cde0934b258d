import { useQuery } from '@tanstack/react-query';
import { useEffect, useMemo } from 'react';
import { useQueryParams } from './use-query-params';
import { getComplementaryProducts } from '../services/complementary-products';
import { useGlobalContext } from '@/store/global';
import { ComplementaryProductsParams } from '@/types/products';
import { getMappedLanguages } from '@/utils/get-mapped-language';
import { transformComplementaryProductsToBundle } from '@/utils/product-utils';

const STALE_TIME = 1000 * 60 * 2; // 2 minutes

export function useComplementaryProducts() {
  const { raw: params, isValid } = useQueryParams();
  const {
    state: { personaHash },
    actions: { setPersonaHash, setPreviousSizeNotFound },
  } = useGlobalContext();

  const complementaryParams = useMemo(() => {
    const defaultParams = {
      tenantId: Number(params.tenantId),
      collectionName: params.collectionName,
      sid: params.sid,
      permalink: params.permalink,
      sizeSystem: params.sizeSystem,
      locale: getMappedLanguages(params.lang).value.backend,
      currency: params.currency,
      similarityThreshold: Number(params.similarityThreshold),
    } as ComplementaryProductsParams;

    if (personaHash) {
      Object.assign(defaultParams, {
        personaHash,
        filterByWhatFitsMe: true,
      });
    }

    return { ...defaultParams };
  }, [params, personaHash]);

  const { data, isLoading, refetch } = useQuery({
    queryKey: ['complementary-products', { ...complementaryParams }],
    queryFn: async () => {
      const response = await getComplementaryProducts(complementaryParams);
      return response;
    },
    enabled: isValid,
    staleTime: STALE_TIME,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  useEffect(() => {
    if (data) {
      const hasFirstProducts = data?.complementary.every(
        (product) => product?.first && Object.keys(product.first).length > 0
      );
      if (!hasFirstProducts && personaHash) {
        setPersonaHash(null);
        setPreviousSizeNotFound(true);
        refetch();
      }
    }
  }, [data, refetch, personaHash, setPersonaHash, setPreviousSizeNotFound]);

  const products = useMemo(() => {
    if (!data) return [];

    return transformComplementaryProductsToBundle(data.complementary, data.baseProduct);
  }, [data]);

  return {
    products,
    isLoading,
  };
}

import { QueryErrorResetBoundary } from '@tanstack/react-query';
import { ErrorBoundary } from 'react-error-boundary';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import { Fallback } from './components/molecules';
import { RootLayout } from './layouts';
import { ComplementarProductsPage } from './pages/complementar-products';
import { DrawerOpenPage } from './pages/drawer-open';
import { SimilarProductsPage } from './pages/similar-products';

export function Router() {
  return (
    <BrowserRouter>
      <QueryErrorResetBoundary>
        {({ reset }) => (
          <ErrorBoundary
            onReset={reset}
            fallbackRender={({ resetErrorBoundary }) => <Fallback resetErrorBoundary={resetErrorBoundary} />}
          >
            <Routes>
              <Route element={<RootLayout />}>
                <Route path="/similar" element={<SimilarProductsPage />} />
                <Route path="/complementary" element={<ComplementarProductsPage />} />
              </Route>
              <Route path="/szb-drawer" element={<DrawerOpenPage />} />
            </Routes>
          </ErrorBoundary>
        )}
      </QueryErrorResetBoundary>
    </BrowserRouter>
  );
}

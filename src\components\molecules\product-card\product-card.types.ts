import { ReactNode } from 'react';

export interface CarouselCard {
  /** URL of the product image */
  imageUrl: string;
  /** Link to the product page */
  link: string;
  /** Alt text for the product image */
  imageAlt: string;
  /** Name of the product */
  name: string;
  /** Price of the product (formatted string) */
  price: string;
  /** Whether the product has a size recommendation */
  hasRecommedation?: boolean;
  /** Recommended size for the product */
  recommendationSize?: string;
  /** ARIA role for the product card */
  role?: string;
  /** Additional CSS classes */
  className?: string;
}

export interface ProductCardProps {
  /** Product data object */
  product: CarouselCard;
  /** Additional props to render inside the card */
  props?: ReactNode;
}

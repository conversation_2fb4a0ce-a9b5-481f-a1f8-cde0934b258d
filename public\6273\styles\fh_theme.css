/* --- E<PERSON><PERSON><PERSON> para o carrossel de SIMILARES --- */
.fh-similar .fh-section__divider {
  background-color: #f0f8ff;
  border-radius: 10px;
  border: 2px solid #0070f3;
}

.fh-similar .fh-section__divider__text {
  color: #0070f3;
  font-size: 18px;
  font-weight: 600;
  font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
}

.fh-similar .fh-product__card__container {
  background-color: #f0f8ff;
  border-radius: 10px;
}

.fh-complementary .fh-product__card__container:hover {
  background-color: #d1fae5;
}

.fh-similar .fh-product__card__title {
  color: #0070f3;
  font-weight: 600;
}

.fh-similar .fh-product__card__price {
  color: #0070f3;
  font-weight: 600;
}

.fh-similar .fh-product__card__badge {
  background-color: #d1fae5;
  border-radius: 50px;
  border: 2px solid #0070f3;
  margin: 10px;
  padding: 15px;
}
.fh-similar .fh-product__card__badge__text {
  color: #065f46;
  font-size: 20px;
  font-weight: 600;
  font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
}

.fh-similar .fh-product__card__img {
  border: 2px solid #0070f3;
  border-radius: 52px;
}

.fh-similar .fh-size__guide__button {
  padding-left: 20px;
  margin-right: 45px;
  margin: 30px;
  padding: 10px;
  background-color: orange;
  border-radius: 50px;
  border: 2px solid #0070f3;
}

.fh-similar .fh-size__guide__button__text {
  color: #0070f3;
  font-size: 20px;
}

.fh-similar .fh-text__filter__container {
  margin: 30px;
  padding: 10px;
}

.fh-similar .fh-text__filter {
  color: #0070f3;
  font-size: 38px;
}

/* --- Estilos para o carrossel de COMPLEMENTARES --- */
.fh-complementary .fh-section__divider {
  background-color: #f0f8ff;
  border-radius: 10px;
  border: 2px solid #f30000;
}

.fh-complementary .fh-section__divider__text {
  color: #f30000;
  font-size: 18px;
  font-weight: 600;
  font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
}

.fh-complementary .fh-size__guide__button__icon {
  background: url(https://static.sizebay.technology/icons/Hanger.svg) center center / 100% 100% no-repeat;
  width: 20px;
  height: 20px;
  display: inline-block;
}

.fh-complementary .fh-size__guide__button__icon svg {
  display: none;
}

.fh-complementary .fh-product__card__container {
  border: 1px dashed #e63946;
  background-color: #fff5f5;
}
.fh-complementary .fh-product__card__title {
  color: #e63946;
  font-style: italic;
}
.fh-complementary .fh-product__card__badge {
  background-color: #ffe0b2;
}
.fh-complementary .fh-product__card__badge__text {
  color: #8d6e63;
}

.fh-form__title {
  color: #af11af;
  font-size: 8px;
  font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
}

.fh-form__submit__button {
  background-color: #0070f3;
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  border-radius: 50px;
  border: 2px solid #0070f3;
}

.fh-toggle__group__item {
  color: #cf4949;
  background-color: #0070f3;
  font-size: 20px;
  border: 2px solid #0070f3;
  border-radius: 10px;
}

.fh-unit__toggle {
  background-color: #0070f3;
  border-radius: 10px;
}

.fh-unit__toggle__label {
  color: #cf4949;
  font-size: 20px;
}

.fh-form__input {
  border: 2px solid #0070f3;
  border-radius: 10px;
}

.fh-form__input__label {
  color: hsl(157, 74%, 47%);
  font-size: 26px;
}

.fh-form__input__helper__text {
  color: #e63946;
  font-size: 20px;
}

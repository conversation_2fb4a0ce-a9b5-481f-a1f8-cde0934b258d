import { Carousel } from '@/components/organisms';
import { useSimilarProducts } from '@/hooks/use-similar-products';
import { useGlobalContext } from '@/store/global';
import { CarouselVariant } from '@/types/carousel';
import { useItemsPerPage } from '@/utils/items-per-page-utils';

export function SimilarProductsPage() {
  const { fetchNextPage, hasNextPage, isFetchingNextPage, products, isLoading } = useSimilarProducts();
  const itemsPerPage = useItemsPerPage({
    itemsType: CarouselVariant.Similar,
  });
  const {
    state: { personaHash },
  } = useGlobalContext();

  return (
    <Carousel
      itemsPerPage={itemsPerPage}
      products={products}
      isLoading={isLoading}
      onLoadMore={() => hasNextPage && fetchNextPage()}
      isFetchingMore={isFetchingNextPage}
      type={CarouselVariant.Similar}
      className={'fh-similar'}
      key={personaHash}
      hasNextPage={hasNextPage}
    />
  );
}

import { containerStyles, dotStateStyles, dotStyles } from './pagination-dots.styles';
import { cn } from '../../../utils/class-utils';

interface PaginationDotsProps {
  /** Total number of pages to display dots for */
  totalPages: number;
  /** Currently active page (zero-based index) */
  currentPage: number;
  /** Callback function when a page dot is clicked */
  onPageChange: (pageIndex: number) => void;
  /** Additional CSS classes to apply to the dots container */
  className?: string;
}

export function PaginationDots({ totalPages, currentPage, onPageChange, className }: PaginationDotsProps) {
  if (totalPages <= 1) return null;

  return (
    <div className={cn(containerStyles, className)} role="tablist" aria-label="Carousel pagination">
      {Array.from({ length: totalPages }).map((_, index) => (
        <button
          key={index}
          onClick={() => onPageChange(index)}
          className={cn(dotStyles, currentPage === index ? dotStateStyles.active : dotStateStyles.inactive)}
          role="tab"
          aria-selected={currentPage === index}
          aria-label={`Page ${index + 1} of ${totalPages}`}
          tabIndex={0}
        />
      ))}
    </div>
  );
}

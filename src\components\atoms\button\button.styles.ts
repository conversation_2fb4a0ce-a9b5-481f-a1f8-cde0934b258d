import { ButtonSize, ButtonVariant } from './button.types';

export const baseStyles = [
  'rounded-lg',
  'inline-flex',
  'items-center',
  'justify-center',
  'gap-3',
  'transition-colors',
  'duration-200',
  'enabled:cursor-pointer',
  'disabled:opacity-50',
  'disabled:cursor-not-allowed',
  'focus-visible:outline-none',
  'focus-visible:ring-2',
  'focus-visible:ring-offset-2',
  'font-medium',
].join(' ');

export const variants: Record<ButtonVariant, string> = {
  primary: 'border bg-[#272727] text-[#FFFFFF] enabled:hover:bg-[#272727]/90',
  secondary: 'border bg-[#DFDFDF] text-[#262626] enabled:hover:bg-[#272727]/10',
  blank: '',
};

export const sizeStyles: Record<ButtonSize, string> = {
  sm: 'px-3 py-2 text-sm',
  md: 'px-4 py-3 text-base',
  lg: 'px-6 py-4 text-lg',
};

export const mappedLanguages = {
  ar: { i18n: 'ar', backend: 'ar-SA' },
  bg: { i18n: 'bg_BG', backend: 'bg-BG' },
  cz: { i18n: 'cs_CZ', backend: 'cs-CZ' },
  dk: { i18n: 'da_DK', backend: 'da-DK' },
  de: { i18n: 'de_DE', backend: 'de-DE' },
  gr: { i18n: 'el_GR', backend: 'el-GR' },
  en: { i18n: 'en', backend: 'en-US' },
  esAR: { i18n: 'es_AR', backend: 'es-AR' },
  esCT: { i18n: 'es_CT', backend: 'es-CT' },
  es: { i18n: 'es_ES', backend: 'es-ES' },
  et: { i18n: 'et_EE', backend: 'et-EE' },
  fi: { i18n: 'fi_FI', backend: 'fi-FI' },
  fr: { i18n: 'fr_FR', backend: 'fr-FR' },
  he: { i18n: 'he_IL', backend: 'he-IL' },
  hr: { i18n: 'hr_HR', backend: 'hr-HR' },
  hu: { i18n: 'hu_HU', backend: 'hu-HU' },
  id: { i18n: 'id_ID', backend: 'id-ID' },
  it: { i18n: 'it_IT', backend: 'it-IT' },
  jp: { i18n: 'ja_JP', backend: 'ja-JP' },
  ko: { i18n: 'ko', backend: 'ko-KR' },
  lt: { i18n: 'lt_LT', backend: 'lt-LT' },
  lv: { i18n: 'lv_LV', backend: 'lv-LV' },
  nl: { i18n: 'nl_NL', backend: 'nl-NL' },
  no: { i18n: 'no_NO', backend: 'no-NO' },
  pl: { i18n: 'pl_PL', backend: 'pl-PL' },
  br: { i18n: 'pt_BR', backend: 'pt-BR' },
  pt: { i18n: 'pt_PT', backend: 'pt-PT' },
  ro: { i18n: 'ro_RO', backend: 'ro-RO' },
  ru: { i18n: 'ru_RU', backend: 'ru-RU' },
  sk: { i18n: 'sk_SK', backend: 'sk-SK' },
  sl: { i18n: 'sl_SI', backend: 'sl-SI' },
  sr: { i18n: 'sr_RS', backend: 'sr-RS' },
  sv: { i18n: 'sv_SE', backend: 'sv-SE' },
  th: { i18n: 'th_TH', backend: 'th-TH' },
  tr: { i18n: 'tr_TR', backend: 'tr-TR' },
  ukr: { i18n: 'uk_UA', backend: 'uk-UA' },
  vn: { i18n: 'vi_VN', backend: 'vi-VN' },
  zh: { i18n: 'zh_CN', backend: 'zh-CN' },
};

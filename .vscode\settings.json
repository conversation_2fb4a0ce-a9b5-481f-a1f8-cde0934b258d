{"editor.defaultFormatter": "dbaeumer.vscode-eslint", "editor.codeActionsOnSave": {"source.addMissingImports.ts": "explicit", "source.fixAll.eslint": "always", "source.organizeImports": "always"}, "[shellscript]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}
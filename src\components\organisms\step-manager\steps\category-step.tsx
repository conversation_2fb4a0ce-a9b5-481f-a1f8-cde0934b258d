import { But<PERSON>, Step, Text } from '@/components/atoms';
import { Clothing, Footwear } from '@/components/icons';
import { ContentBox } from '@/components/molecules/content-box';
import { deleteBodyMeasures } from '@/hooks/use-pocket-vfr-form';
import { postMessageToParent } from '@/hooks/use-post-message';
import { useGlobalContext } from '@/store/global';
import {
  BodyMeasures,
  ClothingStep,
  Gender,
  PocketVFRFormData,
  PocketVFRStep,
  ShoeStep,
} from '@/types/pocket-virtual-fitting-room';
import { cn } from '@/utils/class-utils';
import { t } from 'i18next';
import { useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { CategoryStepProps, StepType } from '../types';

const CategoryStepStyles = {
  root: {
    base: 'flex flex-col gap-2',
    mobile: 'gap-3',
  },
  header: {
    mobile: 'text-center',
  },
  content: {
    container: {
      base: 'flex flex-col gap-2',
      mobile: 'gap-4 mt-2',
    },
    categoryBox: {
      base: 'flex flex-col gap-2 p-3',
      mobile: 'gap-4',
    },
  },
  footer: {
    container: {
      base: 'mt-auto',
      mobile: 'mt-2',
    },
    submitButton: {
      base: 'h-[37px] text-nowrap text-sm font-sans font-normal',
      mobile: 'h-[48px] text-base',
    },
  },
};

export const CategoryStep = ({ isSmallerDevices }: CategoryStepProps) => {
  const {
    actions: { setCurrentStep, setLoadingRecommendation, setPreviousSizeNotFound },
  } = useGlobalContext();

  const { getValues, reset } = useFormContext<PocketVFRFormData>();

  const { gender, isMetric, bodyMeasures } = getValues();
  const { height, heightIn, heightFt, weight, age, bodyShapeChest, bodyShapeWaist, bodyShapeHip } = bodyMeasures || {};

  const clothestDetail = useMemo(() => {
    const finalHeight = isMetric ? height : `${heightFt}'${heightIn}"`;
    const finalWeight = isMetric ? weight : `${weight}lbs`;
    const finalGender = gender === 'M' ? 'Male' : 'Female';

    if (!finalHeight || !finalWeight || !age || !gender) {
      return null;
    }

    return `${finalGender} | ${finalHeight} | ${finalWeight} | ${age} years`;
  }, [height, heightFt, heightIn, isMetric, weight, age, gender]);

  const shoesDetail = null;

  const enableSubmit = useMemo(() => {
    return {
      clothing: clothestDetail !== null,
      shoes: shoesDetail !== null,
      some: clothestDetail !== null || shoesDetail !== null,
    };
  }, [clothestDetail]);

  const handleCategorySelect = (stepTo: PocketVFRStep) => {
    setCurrentStep(stepTo);
  };

  const handleDeleteCategory = (category: StepType) => {
    if (category === StepType.CLOTHING) {
      deleteBodyMeasures(reset, bodyMeasures as BodyMeasures);
    }
  };

  const getRecommendation = () => {
    setPreviousSizeNotFound(false);

    postMessageToParent({
      id: 'szb_get_recommended_size',
      data: {
        gender: gender as Gender,
        height: height as string,
        isMetric,
        weight: weight as string,
        age: age as string,
        bodyShapeChest: bodyShapeChest as string,
        bodyShapeWaist: bodyShapeWaist as string,
        bodyShapeHip: bodyShapeHip as string,
      },
    });

    setLoadingRecommendation(true);
  };

  const ActionsFooterContent = ({ category }: { category: StepType }) => {
    const stepKey = category === StepType.CLOTHING ? ClothingStep.BODY_MEASURES : ShoeStep.SHOE_MEASURES;

    if (enableSubmit[category]) {
      return (
        <div className="flex gap-2">
          <Button type="button" fullWidth variant="secondary" size="sm" onClick={() => handleDeleteCategory(category)}>
            {t('pocket_vfr.clothes.button.delete_profile')}
          </Button>
          <Button type="button" fullWidth variant="secondary" size="sm" onClick={() => handleCategorySelect(stepKey)}>
            {t('pocket_vfr.clothes.button.edit_details')}
          </Button>
        </div>
      );
    }

    return (
      <Button
        fullWidth
        disabled={category === StepType.SHOES}
        type="button"
        size="sm"
        variant="secondary"
        onClick={() => handleCategorySelect(stepKey)}
      >
        {t('pocket_vfr.clothes.button.enter_details')}
      </Button>
    );
  };

  return (
    <Step.Root
      className={cn(CategoryStepStyles.root.base, {
        [CategoryStepStyles.root.mobile]: isSmallerDevices,
      })}
    >
      <Step.Header>
        <Text
          size={isSmallerDevices ? 'md' : 'sm'}
          className={cn('fh-form__title', { [CategoryStepStyles.header.mobile]: isSmallerDevices })}
        >
          {t('pocket_vfr.introduction.caption')}
        </Text>
      </Step.Header>
      <Step.Content
        className={cn(CategoryStepStyles.content.container.base, {
          [CategoryStepStyles.content.container.mobile]: isSmallerDevices,
        })}
      >
        <ContentBox.Root
          className={cn(CategoryStepStyles.content.categoryBox.base, {
            [CategoryStepStyles.content.categoryBox.mobile]: isSmallerDevices,
          })}
        >
          <ContentBox.Main className="inline-flex gap-3">
            <div className="flex items-center">
              <Clothing />
            </div>
            <div>
              <Text size="sm" weight="semibold">
                {t('pocket_vfr.cloth_type.clothes')}
              </Text>
              {enableSubmit[StepType.CLOTHING] ? (
                <Text size="sm">{clothestDetail}</Text>
              ) : (
                <Text size="sm">{t('pocket_vfr.clothes.title')}</Text>
              )}
            </div>
          </ContentBox.Main>
          <ContentBox.Footer>
            <ActionsFooterContent category={StepType.CLOTHING} />
          </ContentBox.Footer>
        </ContentBox.Root>
        <ContentBox.Root
          className={cn(CategoryStepStyles.content.categoryBox.base, {
            [CategoryStepStyles.content.categoryBox.mobile]: isSmallerDevices,
          })}
        >
          <ContentBox.Main className="inline-flex gap-3">
            <div className="flex items-center">
              <Footwear />
            </div>
            <div>
              <Text size="sm" weight="semibold">
                {t('pocket_vfr.cloth_type.shoe')}
              </Text>
              <Text size="sm">{t('pocket_vfr.shoe.title')}</Text>
            </div>
          </ContentBox.Main>
          <ContentBox.Footer>
            <ActionsFooterContent category={StepType.SHOES} />
          </ContentBox.Footer>
        </ContentBox.Root>
      </Step.Content>
      <Step.Footer
        className={cn(CategoryStepStyles.footer.container.base, {
          [CategoryStepStyles.footer.container.mobile]: isSmallerDevices,
        })}
      >
        <Button
          type="button"
          onClick={getRecommendation}
          fullWidth
          className={cn(CategoryStepStyles.footer.submitButton.base, 'fh-form__submit__button', {
            [CategoryStepStyles.footer.submitButton.mobile]: isSmallerDevices,
          })}
          disabled={!enableSubmit['some']}
          size="md"
        >
          {t('pocket_vfr.get_recommendation')}
        </Button>
      </Step.Footer>
    </Step.Root>
  );
};

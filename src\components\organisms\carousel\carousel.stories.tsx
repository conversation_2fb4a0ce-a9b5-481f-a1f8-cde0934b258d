import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Carousel } from './carousel';

const mockProducts = [
  {
    id: '1',
    imageLink: 'https://img.lojasrenner.com.br/item/929127414/original/3.jpg',
    title: 'Camiseta Básica Preta',
    price: 'R$ 89,90',
  },
  {
    id: '2',
    imageLink: 'https://img.lojasrenner.com.br/item/929127415/original/3.jpg',
    title: 'Camiseta Básica Branca',
    price: 'R$ 79,90',
  },
  {
    id: '3',
    imageLink: 'https://img.lojasrenner.com.br/item/929127416/original/3.jpg',
    title: 'Camiseta Básica Azul',
    price: 'R$ 99,90',
  },
  {
    id: '4',
    imageLink: 'https://img.lojasrenner.com.br/item/929127417/original/3.jpg',
    title: 'Camiseta Básica Cinza',
    price: 'R$ 69,90',
  },
];

const meta = {
  title: 'Organisms/Carousel',
  component: Carousel,
  args: {
    type: 'similar',
  },
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
          Um carrossel responsivo e acessível para exibição de produtos.
          
          ### Características
          - Navegação por botões, toque e teclado
          - Preload inteligente de imagens
          - Suporte a infinite scroll
          - Indicadores de página
          - Loading states
          - Totalmente responsivo
          
          ### Acessibilidade
          - Navegável por teclado (←, →, Home, End)
          - ARIA labels apropriados
          - Feedback visual de estados
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    itemsPerPage: {
      description: 'Número de itens visíveis por página',
      control: { type: 'number', min: 1, max: 6 },
      defaultValue: 4,
    },
    products: {
      description: 'Array de produtos a serem exibidos',
      control: 'object',
    },
    isLoading: {
      description: 'Estado de carregamento do carrossel',
      control: 'boolean',
    },
    onLoadMore: {
      description: 'Callback para carregar mais produtos (infinite scroll)',
      control: false,
    },
    isFetchingMore: {
      description: 'Indica se está carregando mais produtos',
      control: 'boolean',
    },
  },
  decorators: [
    (Story) => (
      <div className="w-screen max-w-screen-xl p-4">
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof Carousel>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    itemsPerPage: 4,
    products: mockProducts.map((product) => ({
      ...product,
      link: '#',
      productType: 'clothing',
      gender: 'unisex',
      availability: 'in stock',
      productHash: `hash-${product.id}`,
      itemGroupId: Number(product.id),
      brand: 'Example Brand',
      color: 'default',
      gtin: 123456789,
      additionalImageLinks: [],
    })),
    isLoading: false,
  },
};

export const Loading: Story = {
  args: {
    type: 'similar',
    itemsPerPage: 4,
    products: [],
    isLoading: true,
  },
};

export const Mobile: Story = {
  args: {
    itemsPerPage: 2,
    products: mockProducts.map((product) => ({
      ...product,
      link: '#',
      productType: 'clothing',
      gender: 'unisex',
      availability: 'in stock',
      productHash: `hash-${product.id}`,
      itemGroupId: Number(product.id),
      brand: 'Example Brand',
      color: 'default',
      gtin: 123456789,
      additionalImageLinks: [],
    })),
    isLoading: false,
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
};

export const WithInfiniteScroll: Story = {
  args: {
    itemsPerPage: 4,
    products: mockProducts.map((product) => ({
      ...product,
      link: '#',
      productType: 'clothing',
      gender: 'unisex',
      availability: 'in stock',
      productHash: `hash-${product.id}`,
      itemGroupId: Number(product.id),
      brand: 'Example Brand',
      color: 'default',
      gtin: 123456789,
      additionalImageLinks: [],
    })),
    isLoading: false,
    onLoadMore: () => console.log('Loading more items...'),
    isFetchingMore: false,
  },
};

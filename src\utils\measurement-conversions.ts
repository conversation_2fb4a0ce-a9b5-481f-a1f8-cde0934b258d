export const convertCmToFtIn = (cm: number): { ft: number; inches: number } => {
  const totalInches = cm / 2.54;
  const ft = Math.floor(totalInches / 12);
  const inches = Math.round(totalInches - ft * 12);
  return { ft, inches };
};

export const convertFtInToCm = (ft: number, inches: number): number => {
  const totalInches = ft * 12 + inches;
  return Math.round(totalInches * 2.54);
};

export const convertKgToLb = (kg: number): number => {
  return Math.round(kg * 2.20462);
};

export const convertLbToKg = (lb: number): number => {
  return Math.round(lb / 2.20462);
};

export function normalizeHeight(heightFt: string, heightIn: string) {
  const height = convertFtInToCm(Number(heightFt), Number(heightIn));
  return height;
}

export function normalizeWeight(weight: string, isMetric: boolean) {
  if (isMetric) {
    return weight;
  }
  return convertLbToKg(Number(weight)).toString();
}

import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Select } from './select';

const meta = {
  title: 'Atoms/Select',
  component: Select,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    placeholder: {
      control: 'text',
      description: 'The placeholder text when no value is selected',
    },
    value: {
      control: 'text',
      description: 'The initially selected value',
    },
    options: {
      control: { type: 'object' },
      description: 'Array of available size options',
    },
    onChange: {
      action: 'changed',
      description: 'Callback fired when a new value is selected',
    },
  },
} satisfies Meta<typeof Select>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: 'Select a size',
    value: '',
    onChange: () => {},
    options: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
  },
};

export const WithSelectedValue: Story = {
  args: {
    placeholder: 'Select a size',
    value: 'M',
    onChange: () => {},
    options: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
  },
};

export const WithCustomLabel: Story = {
  args: {
    placeholder: 'Choose your size',
    value: 'XS',
    onChange: () => {},
    options: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
  },
};

export const WithNumericSizes: Story = {
  args: {
    placeholder: 'Numeric sizes',
    value: '40',
    onChange: () => {},
    options: ['36', '38', '40', '42', '44', '46'],
  },
};

export const Interactive = {
  render: () => {
    const InteractiveSelect = () => {
      const [selectedValue, setSelectedValue] = useState('');
      const sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL'];

      return (
        <div className="flex flex-col gap-4">
          <Select placeholder="Select your size" options={sizes} value={selectedValue} onChange={setSelectedValue} />
          {selectedValue && (
            <div className="mt-4 p-2 bg-gray-100 rounded">
              Selected size: <strong>{selectedValue}</strong>
            </div>
          )}
        </div>
      );
    };

    return <InteractiveSelect />;
  },
};

export const ManyOptions: Story = {
  args: {
    placeholder: 'Many options',
    onChange: () => {},
    value: '',
    options: Array.from({ length: 20 }, (_, i) => `Option ${i + 1}`),
  },
};

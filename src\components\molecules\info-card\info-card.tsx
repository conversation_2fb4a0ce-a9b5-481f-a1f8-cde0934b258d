import { useTranslation } from 'react-i18next';
import { Button, Text } from '../../atoms';
import { cn } from '@/utils/class-utils';
import { DeviceType, useDevice } from '@/hooks/use-device';

const InfoCardStyles = {
  container: {
    base: 'flex flex-col gap-3',
    desktop: 'h-max-[22.5rem] carousel-card-height',
    mobile: 'h-full p-3',
  },
  textContainer: {
    base: 'flex justify-center items-center h-1/2 text-center',
    mobile: 'h-[57%] px-3',
  },
  button: {
    container: 'flex flex-col h-full justify-end gap-2',
    base: 'w-full',
  },
};

interface InfoCardProps {
  textInfo: string;
  btnInteractionText: string;
  onClose?: () => void;
  onAction: () => void;
}

export function InfoCard({ textInfo, btnInteractionText, onClose, onAction }: InfoCardProps) {
  const device = useDevice() as DeviceType;
  const isSmallerDevices = [DeviceType.SmallTablet, DeviceType.Mobile].includes(device);
  const { t } = useTranslation();

  return (
    <div
      className={cn(InfoCardStyles.container.base, {
        [InfoCardStyles.container.mobile]: isSmallerDevices,
        [InfoCardStyles.container.desktop]: !isSmallerDevices,
      })}
    >
      <div
        className={cn(InfoCardStyles.textContainer.base, { [InfoCardStyles.textContainer.mobile]: isSmallerDevices })}
      >
        <Text size={isSmallerDevices ? 'md' : 'sm'} color="gray">
          {textInfo}
        </Text>
      </div>
      <div className={InfoCardStyles.button.container}>
        <Button
          variant="primary"
          size={isSmallerDevices ? 'md' : 'sm'}
          className={InfoCardStyles.button.base}
          onClick={onAction}
        >
          {btnInteractionText}
        </Button>
        {onClose ? (
          <Button
            variant="secondary"
            size={isSmallerDevices ? 'md' : 'sm'}
            className={InfoCardStyles.button.base}
            onClick={onClose}
          >
            {t('generics.close')}
          </Button>
        ) : null}
      </div>
    </div>
  );
}

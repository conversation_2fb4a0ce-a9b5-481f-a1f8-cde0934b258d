export enum TrackEvents {
  UNKNOWN_PRODUCT = 'UNKNOWN_PRODUCT',
  INACTIVE_PRODUCT = 'INACTIVE_PRODUCT',
  PLUGIN_IMPORTED = 'PLUGIN_IMPORTED',
  PLUGIN_OPENED = 'P<PERSON><PERSON><PERSON>N_OPENED',
  TABLE_OPENED = 'TABLE_OPENED',
  ANALYSIS_DONE = 'ANALYSIS_DONE',
  REC<PERSON><PERSON><PERSON>ATION_DONE = 'RECOMMENDATION_DONE',
  ADD_TO_CART = 'ADD_TO_CART',
  ORDER = 'ORDER',
  SIMILARITY_RECOMMENDATION_VIEW = 'SIMILARITY_RECOMMENDATION_VIEW',
  SIMILARITY_RECOMMENDATION_CLICK = 'SIMILARITY_RECOMMENDATION_CLICK',
  COMPLEMENTARY_RECOMMENDATION_VIEW = 'COMPLEMENTARY_RECOMMENDATION_VIEW',
  COMPLEMENTARY_RECOMMENDATION_CLICK = 'COMPLEMENTARY_RECOMMENDATION_CLICK',
  RETURN_ORDER = 'R<PERSON><PERSON><PERSON>_ORDER',
}

import { classes } from '../../../utils/class-utils';

export const containerStyles = classes(['sm:mb-0 lg:mb-2 mb-2', 'flex', 'flex-col', 'justify-start', 'items-start']);

export const inputContainerStyles = classes(['flex', 'items-center']);

export const primaryInputStyles = classes(['w-[86px]', 'h-[33px]']);

export const primaryInputImperialHeightStyles = classes(['mr-[6px]', 'w-[40px]']);

export const secondaryInputStyles = classes(['mr-0', 'h-[33px]', 'w-[40px]']);

export const helperTextStyles = classes(['ml-3', 'text-[#272727]', 'font-medium']);

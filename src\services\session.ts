import { sizebayClient } from '../config/sizebay-client';
import { SessionResponse } from '../types/session-types';

export async function getSessionInfo(): Promise<SessionResponse | undefined> {
  try {
    const response = await sizebayClient.getSessionInfo();

    if (!response || typeof response !== 'object') {
      throw new Error('Invalid session info format');
    }

    return {
      sessionId: String(response.sessionId),
      sid: response.sid,
    };
  } catch (error) {
    console.error('Error tracking event:', error);
  }
}

import { classes } from '../../../utils/class-utils';

export const baseStyles = classes([
  'border',
  'border-[#272727]',
  'rounded-md',
  'text-gray-800',
  'text-center',
  'font-inter',
  'text-base',
  'font-normal',
  'leading-normal',
  'focus:outline-none',
  'focus:ring-1',
  'focus:ring-[#272727]',
  'focus:border-[#272727]',
  '[appearance:textfield]',
  '[&::-webkit-outer-spin-button]:appearance-none',
  '[&::-webkit-inner-spin-button]:appearance-none',
]);

export const stateStyles = {
  error: 'border-[#FF4444] focus:ring-[#FF4444]/20 focus:border-[#FF4444]',
  fullWidth: 'w-full',
};

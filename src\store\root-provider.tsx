import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ComponentType, ReactNode } from 'react';
import { GlobalProvider } from './global';

type ProviderProps = {
  children: ReactNode;
};

type Provider = ComponentType<ProviderProps>;

const THIRTY_SECONDS = 30000;

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: THIRTY_SECONDS,
    },
  },
});

const providers: Provider[] = [GlobalProvider];

export function RootProvider({ children }: ProviderProps) {
  return (
    <QueryClientProvider client={queryClient}>
      {providers.reduceRight(
        (acc, Provider) => (
          <Provider>{acc}</Provider>
        ),
        children
      )}
    </QueryClientProvider>
  );
}

import React, { createContext, useCallback, useContext, useEffect, useState } from 'react';
import { DrawerContentProps, DrawerOverlayProps, DrawerProps, DrawerTriggerProps } from './drawer.types';
import { cn } from '../../../utils/class-utils';
import { CloseIcon } from '@/components/icons';

const DrawerStyles = {
  overlay: {
    base: 'fixed inset-0 bg-black/40 z-40 transition-opacity duration-300',
    hidden: 'opacity-0 pointer-events-none',
    visible: 'opacity-100',
  },
  content: {
    base: 'fixed z-50 bg-white shadow-lg transition-transform duration-300 ease-in-out overflow-hidden flex flex-col',
    position: {
      left: 'left-0 top-0 bottom-0',
      right: 'right-0 top-0 bottom-0',
      top: 'top-0 left-0 right-0',
      bottom: 'bottom-0 left-0 right-0',
    },
    size: {
      sm: 'w-[280px]',
      md: 'w-[320px]',
      lg: 'w-[430px]',
      xl: 'w-[520px]',
      full: 'w-full',
      auto: 'w-auto',
    },
    heightSize: {
      sm: 'h-[280px]',
      md: 'h-[320px]',
      lg: 'h-[420px]',
      xl: 'h-[520px]',
      full: 'h-full',
      auto: 'h-auto',
    },
    hidden: {
      left: '-translate-x-full',
      right: 'translate-x-full',
      top: '-translate-y-full',
      bottom: 'translate-y-full',
    },
    visible: {
      left: 'translate-x-0',
      right: 'translate-x-0',
      top: 'translate-y-0',
      bottom: 'translate-y-0',
    },
  },
  closeButton: 'absolute top-4 right-4 p-1 rounded-md hover:bg-gray-100',
  header: 'p-4 border-b',
  body: 'flex-1 overflow-auto',
  footer: 'p-4 border-t',
};

const DrawerContext = createContext<DrawerContextProps | undefined>(undefined);

export interface DrawerContextProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  portalTarget?: HTMLElement;
}

function useDrawerContext() {
  const context = useContext(DrawerContext);
  if (!context) {
    throw new Error('Drawer components must be used within a Drawer');
  }
  return context;
}

export function Drawer({ children, defaultOpen = false, open: controlledOpen, onOpenChange, className }: DrawerProps) {
  const [uncontrolledOpen, setUncontrolledOpen] = useState(defaultOpen);

  const open = controlledOpen !== undefined ? controlledOpen : uncontrolledOpen;
  const setOpen = useCallback(
    (newOpen: boolean) => {
      setUncontrolledOpen(newOpen);
      onOpenChange?.(newOpen);
    },
    [onOpenChange]
  );

  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (open && event.key === 'Escape') {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [open, setOpen]);

  useEffect(() => {
    if (open) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [open]);

  return (
    <DrawerContext.Provider value={{ open, setOpen }}>
      <div className={cn(className)}>{children}</div>
    </DrawerContext.Provider>
  );
}

export function DrawerTrigger({ children, className, asChild = false, ...props }: DrawerTriggerProps) {
  const { setOpen } = useDrawerContext();

  const handleClick = (e: React.MouseEvent<HTMLElement>) => {
    e.preventDefault();
    setOpen(true);
    props.onClick?.(e as unknown as React.MouseEvent<HTMLButtonElement>);
  };

  if (asChild && React.isValidElement(children)) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return React.cloneElement(children as React.ReactElement<any>, {
      onClick: handleClick,
      ...props,
    });
  }

  return (
    <button type="button" className={className} onClick={handleClick} {...props}>
      {children}
    </button>
  );
}

export function DrawerOverlay({ className, closeOnClick = true, ...props }: DrawerOverlayProps) {
  const { open, setOpen } = useDrawerContext();

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    e.preventDefault();
    if (closeOnClick) {
      setOpen(false);
    }
  };

  return (
    <div
      className={cn(
        DrawerStyles.overlay.base,
        open ? DrawerStyles.overlay.visible : DrawerStyles.overlay.hidden,
        className
      )}
      onClick={handleClick}
      {...props}
    />
  );
}

export function DrawerContent({
  children,
  position = 'right',
  size = 'md',
  className,
  showCloseButton = false,
  withHeader = false,
  title,
  withFooter = false,
  footer,
  closeOnOutsideClick = true,
  onClickOutside,
  ...props
}: DrawerContentProps) {
  const { open, setOpen } = useDrawerContext();

  useEffect(() => {
    if (!closeOnOutsideClick) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (target.getAttribute('data-drawer-overlay') === 'true' && open) {
        setOpen(false);
        onClickOutside?.();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [open, setOpen, closeOnOutsideClick, onClickOutside]);

  const isHorizontal = position === 'left' || position === 'right';
  const sizeClass = isHorizontal ? DrawerStyles.content.size[size] : DrawerStyles.content.heightSize[size];

  return (
    <div
      data-drawer-overlay="true"
      className={cn(DrawerStyles.overlay.base, open ? DrawerStyles.overlay.visible : DrawerStyles.overlay.hidden)}
      onClick={(e) => {
        if (closeOnOutsideClick && e.target === e.currentTarget) {
          setOpen(false);
        }
      }}
    >
      <div
        className={cn(
          DrawerStyles.content.base,
          DrawerStyles.content.position[position],
          sizeClass,
          open ? DrawerStyles.content.visible[position] : DrawerStyles.content.hidden[position],
          className,
          'rounded-[20px_20px_0_0]'
        )}
        {...props}
        onClick={(e) => e.stopPropagation()}
      >
        {showCloseButton && (
          <button className={DrawerStyles.closeButton} onClick={() => setOpen(false)} aria-label="Close">
            <CloseIcon />
          </button>
        )}

        {withHeader && (
          <div className={DrawerStyles.header}>{typeof title === 'string' ? <h2>{title}</h2> : title}</div>
        )}

        <div className={DrawerStyles.body}>{children}</div>

        {withFooter && <div className={DrawerStyles.footer}>{footer}</div>}
      </div>
    </div>
  );
}

export function DrawerClose({ children, className, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) {
  const { setOpen } = useDrawerContext();

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    setOpen(false);
    props.onClick?.(e);
  };

  return (
    <button type="button" className={className} onClick={handleClick} {...props}>
      {children}
    </button>
  );
}

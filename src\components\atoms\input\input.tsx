import { InputHTMLAttributes } from 'react';
import { baseStyles, stateStyles } from './input.styles';
import { cn } from '../../../utils/class-utils';

interface InputBaseProps extends InputHTMLAttributes<HTMLInputElement> {
  /** Whether the input is in an error state */
  isError?: boolean;
  /** Whether the input takes up the full width of its container */
  fullWidth?: boolean;
  /** Additional CSS classes to apply */
  className?: string;
}

export function InputBase({ isError, fullWidth = false, className, ...props }: InputBaseProps) {
  return (
    <input
      className={cn(baseStyles, stateStyles.error && isError, stateStyles.fullWidth && fullWidth, className)}
      aria-invalid={isError}
      {...props}
    />
  );
}

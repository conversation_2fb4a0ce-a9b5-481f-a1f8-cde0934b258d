import { Children, cloneElement, isValidElement } from 'react';
import { cn } from '@/utils/class-utils';

export function Slot({ children, className, ...props }: React.HTMLAttributes<HTMLElement>) {
  const child = Children.only(children);

  if (!isValidElement(child)) {
    console.error('Slot component expects a single valid React element as child');
    return null;
  }

  const handleClick = (e: React.MouseEvent<HTMLElement>) => {
    if (props.onClick) {
      props.onClick(e);
    }
    if (child.props.onClick) {
      child.props.onClick(e);
    }
  };

  const newProps = {
    ...props,
    ...child.props,
    onClick: handleClick,
  };

  const combinedClassName = cn(child.props.className, className);

  return cloneElement(child, {
    ...newProps,
    className: combinedClassName,
  });
}

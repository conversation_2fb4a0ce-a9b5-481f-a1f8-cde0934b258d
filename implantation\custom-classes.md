# 🎨 Custom CSS Classes Guide

This guide explains how to use and customize the CSS classes available in the Fashion Hint project. These classes follow the [BEM (Block Element Modifier)](http://getbem.com/) methodology, making it easy to override and extend styles for different tenants or scenarios.

---

## 📦 Where to Define Custom Styles

- **Default classes** are defined in `implantation/fh_classes.css`.
- **Tenant-specific overrides** should be placed in the tenant's theme file, e.g. `public/{tenantId}/styles/fh_theme.css`.

## 🏷️ BEM Naming Convention

- **Block:** `.fh-product__card`
- **Element:** `.fh-product__card__title`
- **Modifier:** `.fh-product__card__container--hover`

---

## 🛠️ How to Use Custom Classes

### 1. **Apply Classes in Components**

All main UI components already include BEM classes.  
Example (`ProductCard`):

```
<div className="fh-product__card__container">
  <img className="fh-product__card__img" />
  <span className="fh-product__card__title">Product Name</span>
</div>
```

---

## 🧩 List of Customizable Classes

### 1. Wrapper Classes 
``` css
.fh-similar
.fh-complementary
```
### 2. Product Card Classes 
``` css
.fh-product__card__container
.fh-product__card__img
.fh-product__card__title
.fh-product__card__price
.fh-product__card__badge
.fh-product__card__badge__text
```
### 3. Pocket VFR Button and Size Filter Classes 
``` css
.fh-size__guide__button
.fh-size__guide__button__icon
.fh-size__guide__button__text
.fh-text__filter__container
.fh-text__filter
```
### 4. Pocket VFR Form Classes 
``` css
.fh-form__title
.fh-form__submit__button
.fh-form__input
.fh-form__input__label
.fh-form__input__helper__text
.fh-toggle__group__item
.fh-unit__toggle
.fh-unit__toggle__label
```
### 5. Section Divider Classes 
``` css
.fh-section__divider
.fh-section__divider__text
```
---

## 📝 Example of Tenant Customization

### 1. Applyng custom style of products cards

```
/* For all product card containers */
.fh-product__card__container {
  background: #fff;
  border-radius: 8px;
}

/* For hover state */
.fh-product__card__container--hover:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

/* For similar products context */
.fh-similar .fh-product__card__container {
  background-color: #f0f8ff;
  border-radius: 10px;
}
```

### 2. Applyng custom style for products cards images

```
/* For all product card images */
.fh-product__card__img {
  border-radius: 8px;
}

/* For similar products context */
.fh-similar .fh-product__card__img {
  border: 2px solid #0070f3;
  border-radius: 52px;
}
```

### 3. Replace Pocket VFR Icon

``` css
.fh-complementary .fh-size__guide__button__icon {
  background: url(https://static.sizebay.technology/icons/Hanger.svg) center center / 100% 100% no-repeat;
  width: 20px;
  height: 20px;
  display: inline-block;
}

.fh-complementary .fh-size__guide__button__icon svg {
  display: none;
}
```
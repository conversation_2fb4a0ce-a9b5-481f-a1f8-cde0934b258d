import { cn } from '@/utils/class-utils';

interface GraduationCapIconProps {
  size?: string | number;
  className?: string;
}

export const GraduationCapIcon = ({ size = '24', className }: GraduationCapIconProps) => (
  <div className={cn('relative', className)}>
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="circle"
      aria-hidden="true"
    >
      <circle cx="12" cy="12" r="12" fill="#272727" />
    </svg>
    <svg
      width="12"
      height="12"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
      aria-hidden="true"
    >
      <path
        d="M7.82401 3.55695C7.82401 3.15314 7.95978 2.90171 8.12679 2.74247C8.30417 2.57334 8.56369 2.46495 8.86425 2.44492C9.49912 2.40259 10.048 2.74231 10.1775 3.26037C10.2637 3.60538 10.2303 3.83106 10.1699 3.98038C10.1083 4.13258 9.99435 4.26494 9.82231 4.37962C9.45599 4.62384 8.91777 4.7241 8.53883 4.7241C8.36852 4.72409 8.20517 4.79175 8.08474 4.91218C7.96431 5.03261 7.89665 5.19595 7.89665 5.36627V6.61202C7.71748 6.65771 7.54229 6.72411 7.3747 6.81119L1.61747 9.80273C-0.648929 10.9804 0.188361 14.4077 2.74247 14.4077H14.2569C16.811 14.4077 17.6483 10.9804 15.3819 9.80273L9.62469 6.81119C9.48158 6.73683 9.33294 6.67756 9.181 6.63337V5.954C9.61805 5.88216 10.1116 5.73039 10.5347 5.44827C10.8715 5.22374 11.1817 4.90378 11.3604 4.46225C11.5403 4.01785 11.5634 3.50847 11.4235 2.94887C11.1005 1.65676 9.83915 1.09272 8.77882 1.16341C8.23131 1.19991 7.67097 1.40248 7.24049 1.81293C6.79964 2.23328 6.53966 2.82998 6.53966 3.55695C6.53966 3.91161 6.82717 4.19913 7.18183 4.19913C7.5365 4.19913 7.82401 3.91161 7.82401 3.55695ZM9.0325 7.95087L14.7897 10.9424C15.8631 11.5002 15.4665 13.1233 14.2569 13.1233H2.74247C1.53284 13.1233 1.13629 11.5002 2.20967 10.9424L7.96689 7.95087C8.30089 7.77732 8.69849 7.77732 9.0325 7.95087Z"
        fill="white"
        stroke="white"
        strokeWidth="0.0858726"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  </div>
);

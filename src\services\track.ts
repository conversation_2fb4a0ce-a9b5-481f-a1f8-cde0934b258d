import { sizebayClient } from '../config/sizebay-client';
import { TrackEventPayload, TrackEventSuccessResponse } from '../types/track-types';

// Todo: Pass personaHash direct to sizebayClient when it's available in track api
export async function trackEvent(params: TrackEventPayload) {
  try {
    const response = await sizebayClient.track(params.eventName, {
      // personaHash: params.personaHash,
      sid: params.sid,
      tenantId: params.tenantId,
      permalink: params.permalink,
      properties: params.properties,
    });

    function isTrackEventSuccess(response: unknown): response is TrackEventSuccessResponse {
      return Boolean(response && typeof response === 'object' && !('error' in response));
    }

    if (isTrackEventSuccess(response)) {
      return;
    }

    throw new Error(Array.isArray(response.message) ? response.message.join(', ') : response.message);
  } catch (error) {
    console.error('Error tracking event:', error);
  }
}

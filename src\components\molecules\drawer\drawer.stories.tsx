import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Drawer, DrawerContent, DrawerTrigger } from './drawer';

const meta: Meta<typeof Drawer> = {
  title: 'Molecules/Drawer',
  component: Drawer,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    className: {
      control: {
        type: 'text',
      },
      description: 'Additional class names for the drawer component.',
      table: {
        type: {
          summary: 'string',
          detail: 'string',
        },
        defaultValue: {
          summary: '',
          detail: '',
        },
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof Drawer>;

export const Default: Story = {
  render: () => (
    <Drawer>
      <DrawerTrigger className="px-4 py-2 bg-blue-500 text-white rounded-md">Open Drawer</DrawerTrigger>
      <DrawerContent>
        <h2 className="text-xl font-bold mb-4">Drawer Content</h2>
        <p>This is the content of the drawer.</p>
      </DrawerContent>
    </Drawer>
  ),
};

export const LeftPosition: Story = {
  render: () => (
    <Drawer>
      <DrawerTrigger className="px-4 py-2 bg-blue-500 text-white rounded-md">Open Left Drawer</DrawerTrigger>
      <DrawerContent position="left">
        <h2 className="text-xl font-bold mb-4">Left Drawer</h2>
        <p>This drawer slides in from the left side.</p>
      </DrawerContent>
    </Drawer>
  ),
};

export const TopPosition: Story = {
  render: () => (
    <Drawer>
      <DrawerTrigger className="px-4 py-2 bg-blue-500 text-white rounded-md">Open Top Drawer</DrawerTrigger>
      <DrawerContent position="top" size="md">
        <h2 className="text-xl font-bold mb-4">Top Drawer</h2>
        <p>This drawer slides in from the top.</p>
      </DrawerContent>
    </Drawer>
  ),
};

export const BottomPosition: Story = {
  render: () => (
    <Drawer>
      <DrawerTrigger className="px-4 py-2 bg-blue-500 text-white rounded-md">Open Bottom Drawer</DrawerTrigger>
      <DrawerContent position="bottom" size="md">
        <h2 className="text-xl font-bold mb-4">Bottom Drawer</h2>
        <p>This drawer slides in from the bottom.</p>
      </DrawerContent>
    </Drawer>
  ),
};

export const WithHeaderAndFooter: Story = {
  render: () => (
    <Drawer>
      <DrawerTrigger className="px-4 py-2 bg-blue-500 text-white rounded-md">
        Open Drawer with Header & Footer
      </DrawerTrigger>
      <DrawerContent
        withHeader
        title="Drawer Title"
        withFooter
        footer={
          <div className="flex justify-end">
            <button className="px-4 py-2 bg-gray-200 rounded-md mr-2">Cancel</button>
            <button className="px-4 py-2 bg-blue-500 text-white rounded-md">Save</button>
          </div>
        }
      >
        <p>This drawer has a header and footer.</p>
        <p className="mt-2">The header shows a title and the footer contains action buttons.</p>
      </DrawerContent>
    </Drawer>
  ),
};

export const DifferentSizes: Story = {
  render: () => (
    <div className="flex space-x-2">
      <Drawer>
        <DrawerTrigger className="px-4 py-2 bg-blue-500 text-white rounded-md">Small Drawer</DrawerTrigger>
        <DrawerContent size="sm">
          <h2 className="text-xl font-bold mb-4">Small Drawer</h2>
          <p>This is a small drawer.</p>
        </DrawerContent>
      </Drawer>

      <Drawer>
        <DrawerTrigger className="px-4 py-2 bg-blue-500 text-white rounded-md">Large Drawer</DrawerTrigger>
        <DrawerContent size="lg">
          <h2 className="text-xl font-bold mb-4">Large Drawer</h2>
          <p>This is a large drawer.</p>
        </DrawerContent>
      </Drawer>

      <Drawer>
        <DrawerTrigger className="px-4 py-2 bg-blue-500 text-white rounded-md">Full Width</DrawerTrigger>
        <DrawerContent size="full">
          <h2 className="text-xl font-bold mb-4">Full Width Drawer</h2>
          <p>This drawer takes up the full width of the screen.</p>
        </DrawerContent>
      </Drawer>
    </div>
  ),
};

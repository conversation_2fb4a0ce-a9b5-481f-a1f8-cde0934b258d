/**
 * Calculates BMI
 *
 * @export
 * @param {string} weight
 * @param {string} height
 * @param {string} gender
 * @returns
 */
export default function getBmi(weight: string, height: string, gender: string) {
  const w = Number(weight);
  const h = Number(height);

  const calc = w / Math.pow(h / 100, 2);

  if (gender === 'M') {
    if (calc < 19.1) return 'A';
    else if (calc <= 25.8) return 'B';
    else if (calc <= 27.3) return 'C';
    else if (calc <= 32.3) return 'D';
    else return 'E';
  } else {
    if (calc < 20.7) return 'A';
    else if (calc <= 26.4) return 'B';
    else if (calc <= 27.8) return 'C';
    else if (calc <= 31.1) return 'D';
    else return 'E';
  }
}

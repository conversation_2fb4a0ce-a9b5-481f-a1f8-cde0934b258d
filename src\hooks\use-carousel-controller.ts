import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { calculateTotalPages, ContainerDimensions } from '@/utils/carousel-utils';

export function useCarouselController({
  onLoadMore,
  isFetchingMore,
  itemsPerPage: initialItemsPerPage,
  totalItems,
  hasNextPage,
}: {
  onLoadMore?: () => void;
  isFetchingMore: boolean;
  itemsPerPage: number;
  totalItems: number;
  hasNextPage: boolean;
}): {
  canScrollLeft: boolean;
  canScrollRight: boolean;
  totalPages: number;
  currentPage: number;
  contentRef: React.RefObject<HTMLDivElement>;
  handlers: {
    navigation: {
      onPrevious: () => void;
      onNext: () => void;
      onPageChange: (page: number) => void;
    };
    keyboard: {
      onKeyDown: (e: KeyboardEvent) => void;
    };
  };
} {
  const contentRef = useRef<HTMLDivElement>(null);
  const isScrollingProgrammatically = useRef(false);
  const initialState = useMemo(
    () => ({
      isTransitioning: false,
      currentPage: 0,
      canScrollLeft: false,
      canScrollRight: false,
      itemsPerPage: initialItemsPerPage,
    }),
    [initialItemsPerPage]
  );
  const [isTransitioning, setIsTransitioning] = useState(initialState.isTransitioning);
  const [currentPage, setCurrentPage] = useState(initialState.currentPage);
  const [canScrollLeft, setCanScrollLeft] = useState(initialState.canScrollLeft);
  const [canScrollRight, setCanScrollRight] = useState(initialState.canScrollRight);
  const [itemsPerPage, setItemsPerPage] = useState(initialState.itemsPerPage);

  useEffect(() => {
    setItemsPerPage(initialItemsPerPage);
  }, [initialItemsPerPage]);

  const TRANSITION_DURATION = 300;
  const GAP = 20;

  const getDimensions = useCallback((): ContainerDimensions | null => {
    const containerRef = contentRef.current;
    if (!containerRef) return null;

    const itemWidth = containerRef.firstElementChild?.getBoundingClientRect().width ?? 0;
    const currentScroll = containerRef.scrollLeft;
    const scrollableWidth = containerRef.scrollWidth - containerRef.clientWidth;
    const remainingScroll = scrollableWidth - currentScroll;
    const itemTotalWidth = itemWidth + GAP;

    return {
      container: containerRef,
      itemWidth,
      currentScroll,
      scrollableWidth,
      remainingScroll,
      itemTotalWidth,
    };
  }, [contentRef]);

  const getTotalPages = useCallback((): number => {
    return calculateTotalPages(totalItems, itemsPerPage);
  }, [totalItems, itemsPerPage]);

  const updateScrollState = useCallback(() => {
    const dimensions = getDimensions();
    if (!dimensions) return;

    setCanScrollLeft(currentPage > 0);

    const hasMoreItems = currentPage < getTotalPages() - 1;

    setCanScrollRight(hasMoreItems || hasNextPage);
  }, [getDimensions, currentPage, getTotalPages, hasNextPage]);

  const scrollTo = useCallback((scrollLeft: number) => {
    const container = contentRef.current;
    if (!container) return;

    isScrollingProgrammatically.current = true;

    container.scrollTo({
      left: scrollLeft,
      behavior: 'smooth',
    });

    setTimeout(() => {
      isScrollingProgrammatically.current = false;
    }, TRANSITION_DURATION + 50);
  }, []);

  const shouldLoadMore = useCallback(() => {
    const dimensions = getDimensions();
    if (!dimensions) return false;

    const scrollThreshold = Math.ceil(0.8 * dimensions.scrollableWidth);
    const allowedToPaginate = dimensions.currentScroll > scrollThreshold;

    return allowedToPaginate && !isFetchingMore && onLoadMore;
  }, [getDimensions, isFetchingMore, onLoadMore]);

  const handleLoadMore = useCallback(() => {
    if (shouldLoadMore()) {
      onLoadMore?.();
    }
  }, [shouldLoadMore, onLoadMore]);

  const calculateCurrentPage = useCallback(() => {
    const dimensions = getDimensions();
    if (!dimensions) return 0;
    const { currentScroll, scrollableWidth, itemTotalWidth } = dimensions;

    if (Math.abs(currentScroll - scrollableWidth) < 10) {
      return getTotalPages() - 1;
    }

    const pageWidth = itemTotalWidth * itemsPerPage;
    const newPage = Math.round(currentScroll / pageWidth);

    return Math.max(0, Math.min(newPage, getTotalPages() - 1));
  }, [getDimensions, itemsPerPage, getTotalPages]);

  const scrollToNext = useCallback(() => {
    const dimensions = getDimensions();
    if (!dimensions) return;
    const { itemTotalWidth } = dimensions;

    const nextPage = currentPage + 1;
    if (nextPage >= getTotalPages()) return;

    const hasReachedScrollThreshold = shouldLoadMore();
    if (hasReachedScrollThreshold && hasNextPage) {
      handleLoadMore();
    }

    const pageWidth = itemTotalWidth * itemsPerPage;
    const exactScrollPosition = nextPage * pageWidth;

    setCurrentPage(nextPage);
    scrollTo(exactScrollPosition);
  }, [getDimensions, currentPage, getTotalPages, shouldLoadMore, hasNextPage, itemsPerPage, scrollTo, handleLoadMore]);

  const scrollToPrevious = useCallback(() => {
    const dimensions = getDimensions();
    if (!dimensions) return;
    const { itemTotalWidth } = dimensions;

    // No idea why this is needed, but it works
    const previousPage = Math.max(0, currentPage - 1);
    const pageWidth = itemTotalWidth * itemsPerPage;
    const exactScrollPosition = previousPage * pageWidth;

    setCurrentPage(previousPage);
    scrollTo(exactScrollPosition);
  }, [getDimensions, scrollTo, itemsPerPage, currentPage]);

  const scrollToStart = useCallback(() => {
    setCurrentPage(0);
    scrollTo(0);
  }, [scrollTo]);

  const scrollToEnd = useCallback(() => {
    const dimensions = getDimensions();
    if (!dimensions) return;

    const lastPage = getTotalPages() - 1;
    setCurrentPage(lastPage);
    scrollTo(dimensions.scrollableWidth);
    handleLoadMore();
  }, [getDimensions, scrollTo, handleLoadMore, getTotalPages]);

  const scrollToPage = useCallback(
    (page: number) => {
      const dimensions = getDimensions();
      if (!dimensions) return;
      const { itemTotalWidth } = dimensions;

      const targetPage = Math.max(0, Math.min(page, getTotalPages() - 1));
      setCurrentPage(targetPage);
      // No idea why this is needed, but it works

      const exactScrollPosition = targetPage * itemTotalWidth * itemsPerPage;
      scrollTo(exactScrollPosition);

      if (page === getTotalPages() - 1) {
        handleLoadMore();
      }
    },
    [getDimensions, scrollTo, itemsPerPage, getTotalPages, handleLoadMore]
  );

  const handleSlide = useCallback(
    (direction: 'left' | 'right') => {
      if (isTransitioning) return;

      setIsTransitioning(true);
      if (direction === 'right') scrollToNext();
      else scrollToPrevious();

      setTimeout(() => {
        setIsTransitioning(false);
      }, TRANSITION_DURATION);
    },
    [isTransitioning, scrollToNext, scrollToPrevious]
  );

  const handleTabNavigation = useCallback(
    (e: KeyboardEvent) => {
      const dimensions = getDimensions();
      if (!dimensions?.container) return;

      const focusableElements = dimensions.container.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      const focusedElement = document.activeElement;
      const focusedIndex = Array.from(focusableElements).indexOf(focusedElement as Element);

      if (!e.shiftKey && canScrollRight && focusedIndex === focusableElements.length - 1) {
        handleSlide('right');
      } else if (e.shiftKey && canScrollLeft && focusedIndex === 0) {
        handleSlide('left');
      }
    },
    [getDimensions, canScrollRight, canScrollLeft, handleSlide]
  );

  useEffect(() => {
    updateScrollState();
  }, [updateScrollState, currentPage, totalItems]);

  useEffect(() => {
    const container = contentRef.current;
    if (!container) return;

    const handleScroll = () => {
      // No idea why this is needed, but it works
      if (isScrollingProgrammatically.current) {
        return;
      }

      updateScrollState();

      // No idea why this is needed, but it works
      const newPage = calculateCurrentPage();
      if (newPage !== currentPage) {
        setCurrentPage(newPage);
      }

      handleLoadMore();
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [calculateCurrentPage, handleLoadMore, updateScrollState, currentPage]);

  const navigationHandlers = {
    onPrevious: () => canScrollLeft && handleSlide('left'),
    onNext: () => canScrollRight && handleSlide('right'),
    onPageChange: scrollToPage,
  };

  const keyboardHandlers = {
    onKeyDown: (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          if (canScrollLeft) handleSlide('left');
          break;

        case 'ArrowRight':
          e.preventDefault();
          if (canScrollRight) handleSlide('right');
          break;

        case 'Home':
          e.preventDefault();
          scrollToStart();
          break;

        case 'End':
          e.preventDefault();
          scrollToEnd();
          break;

        case 'Tab':
          handleTabNavigation(e);
          break;
      }
    },
  };

  return {
    canScrollLeft,
    canScrollRight,
    totalPages: getTotalPages(),
    currentPage,
    contentRef,
    handlers: {
      navigation: navigationHandlers,
      keyboard: keyboardHandlers,
    },
  };
}

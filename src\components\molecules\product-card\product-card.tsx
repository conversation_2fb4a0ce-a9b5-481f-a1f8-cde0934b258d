import { useTranslation } from 'react-i18next';
import { LazyLoadImage, Text } from '@/components/atoms';
import { Badge } from '@/components/atoms/badge';
import { TextSize } from '@/components/atoms/text/text.styles';
import { CheckIcon } from '@/components/icons';
import { useQueryParams } from '@/hooks';
import { DeviceType, useDevice } from '@/hooks/use-device';
import { ProductCardAlignment, ProductCardType } from '@/types/carousel';
import { cn } from '@/utils/class-utils';
import { formatPrice } from '@/utils/format-price';

interface suitableSizes {
  size: string;
  comfortable: number;
  value: boolean;
}

interface CarouselCard {
  imageUrl: string;
  link: string;
  imageAlt: string;
  name: string;
  price: string;
  hasRecommedation?: boolean;
  recommendationSize?: string;
  suitableSizes?: suitableSizes[];
  role?: string;
  className?: string;
  pageNum?: number;
  itemPosition?: ProductCardType;
}

interface ProductCardProps {
  product: CarouselCard;
  alignment?: ProductCardAlignment;
}

const TEXT_PROPS = {
  oneColumn: {
    titleText: { size: 'md', lineBreak: 'truncate' },
    priceText: { size: 'lg' },
  },
  twoColumns: {
    titleText: { size: 'md', lineBreak: 'line-clamp-2' },
    priceText: { size: 'md' },
  },
};

const VariantStyles = {
  container: {
    oneColumn: 'gap-3 flex flex-col flex-shrink-0 similar-carousel-card cursor-pointer',
    twoColumns: 'grid grid-cols-[auto,1fr] p-1 gap-1 hover:bg-[#F7F7F7]',
  },
  imageContainer: {
    image: {
      oneColumn: 'rounded-lg',
      twoColumns: 'absolute inset-0 m-auto h-auto rounded',
    },
    container: {
      oneColumn: 'h-full min-h-full w-full aspect-[4/5] rounded-lg',
      twoColumns: 'relative image-card-variant-height content-center flex-nowrap aspect-[4/4] rounded',
    },
  },
  infoContainer: {
    container: {
      oneColumn: 'flex flex-col gap-3',
      twoColumns: 'flex flex-col justify-center text-left gap-1 pr-1 w-full',
    },
    badge: {
      twoColumns: 'h-[1.563rem] w-fit px-2',
      mobile: 'text-xs',
    },
  },
};

export function ProductCard({ product, alignment = ProductCardAlignment.OneColumn }: ProductCardProps) {
  const { raw: params } = useQueryParams();
  const { t } = useTranslation();
  const device = useDevice() as DeviceType;
  const { className, suitableSizes, imageAlt, imageUrl, name, role } = product;
  const hasRecommedation = product?.suitableSizes?.length ? true : false;
  const trackPayload: { product: CarouselCard; recommendationSize?: string } = {
    product,
  };
  const recommendationSize = suitableSizes?.length
    ? suitableSizes.reduce(
        (closest, current) => (Math.abs(current.comfortable) < Math.abs(closest.comfortable) ? current : closest),
        suitableSizes[0]
      )?.size
    : undefined;

  if (hasRecommedation) {
    if (typeof recommendationSize === 'string') {
      trackPayload.recommendationSize = recommendationSize;
    }
  }

  return (
    <div
      role={role}
      className={cn(
        'fh-product__card__container product-card',
        alignment === ProductCardAlignment.OneColumn
          ? VariantStyles.container.oneColumn
          : VariantStyles.container.twoColumns,
        className
      )}
      data-trackdata={JSON.stringify(trackPayload)}
    >
      <div className={cn(alignment === ProductCardAlignment.TwoColumns && 'p-3')}>
        <div
          className={cn(
            'flex justify-center overflow-hidden',
            alignment === ProductCardAlignment.OneColumn && VariantStyles.imageContainer.container.oneColumn,
            alignment === ProductCardAlignment.TwoColumns && VariantStyles.imageContainer.container.twoColumns
          )}
        >
          <LazyLoadImage
            src={imageUrl}
            alt={imageAlt}
            className={cn(
              'fh-product__card__img w-full h-full object-cover',
              alignment === ProductCardAlignment.TwoColumns && VariantStyles.imageContainer.image.twoColumns,
              alignment === ProductCardAlignment.OneColumn && VariantStyles.imageContainer.image.oneColumn
            )}
            draggable={false}
          />
        </div>
      </div>
      <div
        className={cn(
          alignment === ProductCardAlignment.TwoColumns && VariantStyles.infoContainer.container.twoColumns,
          alignment === ProductCardAlignment.OneColumn && VariantStyles.infoContainer.container.oneColumn
        )}
      >
        <Text
          size={TEXT_PROPS[alignment].titleText.size as TextSize}
          color="black"
          className={cn(TEXT_PROPS[alignment].titleText.lineBreak, 'fh-product__card__title')}
        >
          {name ? name.charAt(0).toUpperCase() + name.slice(1) : name}
        </Text>
        <Text
          className="fh-product__card__price"
          size={TEXT_PROPS[alignment].titleText.size as TextSize}
          weight="semibold"
        >
          {formatPrice(product.price, params?.currency)}
        </Text>
        {hasRecommedation && recommendationSize && (
          <Badge
            variant="default"
            size="sm"
            className={cn(
              'fh-product__card__badge',
              alignment === ProductCardAlignment.TwoColumns && VariantStyles.infoContainer.badge.twoColumns,
              [DeviceType.Mobile, DeviceType.SmallTablet].includes(device) && VariantStyles.infoContainer.badge.mobile
            )}
          >
            <CheckIcon />
            <Text className="fh-product__card__badge__text" size="sm" color="black" weight="regular">
              {t('fashion_hint.recommended_size', { size: recommendationSize })}
            </Text>
          </Badge>
        )}
      </div>
    </div>
  );
}

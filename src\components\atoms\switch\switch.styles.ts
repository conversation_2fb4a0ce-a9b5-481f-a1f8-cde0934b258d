import { SwitchSize, SwitchVariant } from './switch.types';

export const rootSizeStyles: Record<SwitchSize, string> = {
  sm: 'h-[18px] w-8',
  md: 'h-6 w-11',
  lg: 'h-7 w-[52px]',
};

export const thumbSizeStyles: Record<SwitchSize, string> = {
  sm: 'h-[14px] w-[14px] data-[state=checked]:translate-x-[14px]',
  md: 'h-5 w-5 data-[state=checked]:translate-x-5',
  lg: 'h-6 w-6 data-[state=checked]:translate-x-6',
};

export const variantStyles: Record<SwitchVariant, string> = {
  default: ['bg-[#272727]'].join(' '),
  primary: ['bg-[#272727]'].join(' '),
  danger: [
    'data-[state=unchecked]:bg-[#E4E4E7]',
    'data-[state=checked]:bg-[#DC2626]',
    'hover:data-[state=unchecked]:bg-[#D4D4D8]',
    'hover:data-[state=checked]:bg-[#B91C1C]',
  ].join(' '),
};

export const rootBaseStyles = [
  'peer inline-flex shrink-0 cursor-pointer items-center rounded-full',
  'border-2 border-transparent transition-colors',
  'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
  'disabled:cursor-not-allowed disabled:opacity-50',
  'touch-none select-none',
].join(' ');

export const thumbBaseStyles = [
  'pointer-events-none block rounded-full bg-white',
  'shadow-lg ring-0 transition-transform',
  'data-[state=checked]:translate-x-5',
  'data-[state=unchecked]:translate-x-0',
  'will-change-transform',
].join(' ');

import { SizeGuideKeys, ToggleOption, ToggleOptions } from '@/types/pocket-virtual-fitting-room';

export const MEASUREMENT_LIMITS = {
  height: {
    feet: {
      min: 0,
      max: 7,
    },
    inches: {
      min: 0,
      max: 11,
    },
    cm: {
      min: 0,
      max: 300,
    },
  },
  weight: {
    kg: {
      min: 0,
      max: 300,
    },
    lb: {
      min: 0,
      max: 485,
    },
  },
  age: {
    min: 0,
    max: 120,
  },
} as const;

export const GENDER_OPTIONS = {
  FEMALE: 'F',
  MALE: 'M',
} as const;

export const SIZE_GUIDE_KEYS: Record<SizeGuideKeys, string> = {
  gender: 'gender',
  isMetric: 'isMetric',
  age: 'bodyMeasures.age',
  weight: 'bodyMeasures.weight',
  height: 'bodyMeasures.height',
  heightFt: 'bodyMeasures.heightFt',
  heightIn: 'bodyMeasures.heightIn',
  bodyShapeChest: 'bodyMeasures.bodyShapeChest',
  bodyShapeWaist: 'bodyMeasures.bodyShapeWaist',
  bodyShapeHip: 'bodyMeasures.bodyShapeHip',
};

export const NAVIGATION_OPTIONS = {
  PREVIOUS: 'previous',
  NEXT: 'next',
  SAVE: 'save',
} as const;

export const TOGGLE_LABELS = {
  MALE: 'pocket_vfr.gender.male',
  FEMALE: 'pocket_vfr.gender.female',
  PREVIOUS: 'Go back',
  NEXT: 'Next',
  SAVE: 'Save details',
} as const;

export const TOGGLE_OPTIONS: ToggleOptions = {
  gender: [
    { value: GENDER_OPTIONS.FEMALE, label: TOGGLE_LABELS.FEMALE },
    { value: GENDER_OPTIONS.MALE, label: TOGGLE_LABELS.MALE },
  ],
  navigation: [
    { value: NAVIGATION_OPTIONS.PREVIOUS, label: TOGGLE_LABELS.PREVIOUS },
    { value: NAVIGATION_OPTIONS.NEXT, label: TOGGLE_LABELS.NEXT },
    { value: NAVIGATION_OPTIONS.SAVE, label: TOGGLE_LABELS.SAVE },
  ],
};

export const NAVIGATION_PREVIOUS: ToggleOption = {
  value: NAVIGATION_OPTIONS.PREVIOUS,
  label: TOGGLE_LABELS.PREVIOUS,
};

export const NAVIGATION_NEXT: ToggleOption = {
  value: NAVIGATION_OPTIONS.NEXT,
  label: TOGGLE_LABELS.NEXT,
};

export const NAVIGATION_SAVE: ToggleOption = {
  value: NAVIGATION_OPTIONS.SAVE,
  label: TOGGLE_LABELS.SAVE,
};

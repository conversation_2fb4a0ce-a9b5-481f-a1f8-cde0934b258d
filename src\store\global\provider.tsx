import React, { useEffect, useMemo, useReducer } from 'react';
import { GlobalContext } from './context';
import { GlobalActionTypes, initialState, sessionReducer } from './reducer';
import { GlobalContextData } from './types';
import { PocketVFRStep } from '@/types/pocket-virtual-fitting-room';

interface GlobalProviderProps {
  children: React.ReactNode;
}

export function GlobalProvider({ children }: GlobalProviderProps) {
  const [state, dispatch] = useReducer(sessionReducer, initialState);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data) {
        switch (event.data.id) {
          case 'szb_cookies_update': {
            const { sid, personaHash } = event.data;

            dispatch({
              kind: GlobalActionTypes.GET_SESSION_INFO,
              payload: {
                sessionId: sid || '',
                personaHash: personaHash || null,
              },
            });
            break;
          }
          case 'szb_error_creating_profile': {
            dispatch({
              kind: GlobalActionTypes.SET_ERROR_PROFILE,
              payload: true,
            });
            break;
          }
        }
      }
    };

    window.parent.postMessage({ id: 'szb_iframe_context_ready' }, '*');
    window.addEventListener('message', handleMessage);

    return () => window.removeEventListener('message', handleMessage);
  }, []);

  const setErrorProfile = (value: boolean | null) => {
    dispatch({
      kind: GlobalActionTypes.SET_ERROR_PROFILE,
      payload: value,
    });
  };

  const setPersonaHash = (value: string | null) => {
    dispatch({
      kind: GlobalActionTypes.SET_PERSONA_HASH,
      payload: value,
    });
  };

  const setPreviousSizeNotFound = (value: boolean) => {
    dispatch({
      kind: GlobalActionTypes.SET_PREVIOUS_SIZE_NOT_FOUND,
      payload: value,
    });
  };

  const setCurrentStep = (value: PocketVFRStep) => {
    dispatch({
      kind: GlobalActionTypes.SET_CURRENT_STEP,
      payload: value,
    });
  };

  const setLoadingRecommendation = (value: boolean) => {
    dispatch({
      kind: GlobalActionTypes.SET_LOADING_RECOMMENDATION,
      payload: value,
    });
  };

  const contextValue = useMemo(
    () =>
      ({
        state,
        actions: {
          setErrorProfile,
          setPersonaHash,
          setPreviousSizeNotFound,
          setCurrentStep,
          setLoadingRecommendation,
        },
      }) as GlobalContextData,
    [state]
  );

  return <GlobalContext.Provider value={contextValue}>{children}</GlobalContext.Provider>;
}

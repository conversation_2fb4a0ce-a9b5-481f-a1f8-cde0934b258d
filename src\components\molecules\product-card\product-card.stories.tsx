import type { Meta, StoryObj } from '@storybook/react';
import { ProductCard } from './product-card';
import { ProductCardAlignment, ProductCardPosition } from '@/types/carousel';

const meta = {
  title: 'Molecules/ProductCard',
  component: ProductCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    product: {
      description: 'Product data object containing all necessary information',
      control: 'object',
      table: {
        type: {
          summary: 'CarouselCard',
          detail: `{
            imageUrl: string;
            imageAlt: string;
            name: string;
            price: string;
            hasRecommedation?: boolean;
            recommendationSize?: string;
            link: string;
            itemPosition?: ProductCardPosition;
          }`,
        },
      },
    },
    alignment: {
      description: 'Layout alignment of the product card',
      control: 'select',
      options: Object.values(ProductCardAlignment),
      table: {
        type: { summary: 'ProductCardAlignment' },
        defaultValue: { summary: 'ProductCardAlignment.OneColumn' },
      },
    },
  },
} satisfies Meta<typeof ProductCard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    product: {
      imageUrl: 'https://img.lojasrenner.com.br/item/929127414/original/3.jpg',
      imageAlt: 'Camiseta Básica Preta',
      name: 'Camiseta Básica Preta',
      price: 'R$ 89,90',
      hasRecommedation: false,
      recommendationSize: '',
      link: '',
    },
    alignment: ProductCardAlignment.OneColumn,
  },
};

export const TwoColumnsVariant: Story = {
  args: {
    product: {
      ...Default.args.product,
    },
    alignment: ProductCardAlignment.TwoColumns,
  },
};

export const WithRecommendation: Story = {
  args: {
    product: {
      ...Default.args.product,
      hasRecommedation: true,
      recommendationSize: 'M',
    },
  },
};

export const TwoColumnsWithRecommendation: Story = {
  args: {
    product: {
      ...Default.args.product,
      hasRecommedation: true,
      recommendationSize: 'M',
    },
    alignment: ProductCardAlignment.TwoColumns,
  },
};

export const LongProductName: Story = {
  args: {
    product: {
      ...Default.args.product,
      name: 'Camiseta Básica Preta com Detalhes em Algodão Pima e Acabamento Premium',
    },
  },
};

export const LongProductNameTwoColumns: Story = {
  args: {
    product: {
      ...Default.args.product,
      name: 'Camiseta Básica Preta com Detalhes em Algodão Pima e Acabamento Premium',
    },
    alignment: ProductCardAlignment.TwoColumns,
  },
};

export const WithCustomClass: Story = {
  args: {
    product: {
      ...Default.args.product,
      className: 'border border-gray-200 rounded-lg p-2',
    },
  },
};

export const WithFirstPosition: Story = {
  args: {
    product: {
      ...Default.args.product,
      itemPosition: ProductCardPosition.First,
    },
  },
};

export const WithMiddlePosition: Story = {
  args: {
    product: {
      ...Default.args.product,
      itemPosition: ProductCardPosition.Middle,
    },
  },
};

export const WithLastPosition: Story = {
  args: {
    product: {
      ...Default.args.product,
      itemPosition: ProductCardPosition.Last,
    },
  },
};

export const TwoColumnsWithFirstPosition: Story = {
  args: {
    product: {
      ...Default.args.product,
      itemPosition: ProductCardPosition.First,
    },
    alignment: ProductCardAlignment.TwoColumns,
  },
};

export const TwoColumnsWithLastPosition: Story = {
  args: {
    product: {
      ...Default.args.product,
      itemPosition: ProductCardPosition.Last,
    },
    alignment: ProductCardAlignment.TwoColumns,
  },
};

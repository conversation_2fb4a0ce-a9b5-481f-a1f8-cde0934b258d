import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { Text } from '../../../components/atoms';

describe('Text', () => {
  describe('Rendering', () => {
    it('should render with default props', () => {
      render(<Text size="md">Default text</Text>);
      expect(screen.getByText('Default text')).toBeInTheDocument();
    });

    it('should render with base styles', () => {
      render(<Text size="md">Base styles</Text>);
      const text = screen.getByText('Base styles');
      expect(text.tagName.toLowerCase()).toBe('p');
      expect(text).toHaveClass('text-[#272727]');
      expect(text).toHaveClass('text-[16px]');
      expect(text).toHaveClass('font-normal');
    });

    it('should render different HTML elements based on "as" prop', () => {
      const { rerender } = render(
        <Text size="md" as="h1">
          Heading 1
        </Text>
      );
      expect(screen.getByText('Heading 1').tagName.toLowerCase()).toBe('h1');

      rerender(
        <Text size="md" as="span">
          Span text
        </Text>
      );
      expect(screen.getByText('Span text').tagName.toLowerCase()).toBe('span');

      rerender(
        <Text size="md" as="div">
          Div text
        </Text>
      );
      expect(screen.getByText('Div text').tagName.toLowerCase()).toBe('div');
    });
  });

  describe('Sizes', () => {
    it('should render all different sizes correctly', () => {
      const { rerender } = render(<Text size="xl">Extra Large</Text>);
      expect(screen.getByText('Extra Large')).toHaveClass('text-[20px]');

      rerender(<Text size="lg">Large</Text>);
      expect(screen.getByText('Large')).toHaveClass('text-[18px]');

      rerender(<Text size="md">Medium</Text>);
      expect(screen.getByText('Medium')).toHaveClass('text-[16px]');

      rerender(<Text size="sm">Small</Text>);
      expect(screen.getByText('Small')).toHaveClass('text-[14px]');

      rerender(<Text size="xs">Extra Small</Text>);
      expect(screen.getByText('Extra Small')).toHaveClass('text-[12px]');
    });
  });

  describe('Weights', () => {
    it('should render all different weights correctly', () => {
      const { rerender } = render(
        <Text size="md" weight="regular">
          Regular
        </Text>
      );
      expect(screen.getByText('Regular')).toHaveClass('font-normal');

      rerender(
        <Text size="md" weight="medium">
          Medium
        </Text>
      );
      expect(screen.getByText('Medium')).toHaveClass('font-medium');

      rerender(
        <Text size="md" weight="semibold">
          Semibold
        </Text>
      );
      expect(screen.getByText('Semibold')).toHaveClass('font-semibold');
    });
  });

  describe('Colors', () => {
    it('should render all different colors correctly', () => {
      const { rerender } = render(
        <Text size="md" color="black">
          Black
        </Text>
      );
      expect(screen.getByText('Black')).toHaveClass('text-[#272727]');

      rerender(
        <Text size="md" color="white">
          White
        </Text>
      );
      expect(screen.getByText('White')).toHaveClass('text-[#FFFFFF]');

      rerender(
        <Text size="md" color="gray">
          Gray
        </Text>
      );
      expect(screen.getByText('Gray')).toHaveClass('text-[#5D5D5D]');
    });
  });

  describe('Styling', () => {
    it('should accept and apply custom className', () => {
      render(
        <Text size="md" className="custom-class">
          Custom
        </Text>
      );
      expect(screen.getByText('Custom')).toHaveClass('custom-class');
    });

    it('should maintain base styles when custom className is provided', () => {
      render(
        <Text size="md" weight="medium" color="black" className="custom-class">
          Custom with base styles
        </Text>
      );

      const text = screen.getByText('Custom with base styles');
      expect(text).toHaveClass('custom-class');
      expect(text).toHaveClass('text-[#272727]');
      expect(text).toHaveClass('font-medium');
      expect(text).toHaveClass('text-[16px]');
    });
  });

  describe('Composition', () => {
    it('should combine all props correctly', () => {
      render(
        <Text size="lg" weight="semibold" color="black" as="h2" className="custom-class">
          Combined props
        </Text>
      );

      const text = screen.getByText('Combined props');
      expect(text.tagName.toLowerCase()).toBe('h2');
      expect(text).toHaveClass('text-[18px]');
      expect(text).toHaveClass('font-semibold');
      expect(text).toHaveClass('text-[#272727]');
      expect(text).toHaveClass('custom-class');
    });
  });

  describe('Accessibility', () => {
    it('should maintain proper heading hierarchy when used as headings', () => {
      render(
        <>
          <Text size="xl" as="h1">
            Main Heading
          </Text>
          <Text size="lg" as="h2">
            Sub Heading
          </Text>
          <Text size="md" as="h3">
            Section Heading
          </Text>
        </>
      );

      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Main Heading');
      expect(screen.getByRole('heading', { level: 2 })).toHaveTextContent('Sub Heading');
      expect(screen.getByRole('heading', { level: 3 })).toHaveTextContent('Section Heading');
    });
  });
});

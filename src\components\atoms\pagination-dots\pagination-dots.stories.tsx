import type { Meta, StoryObj } from '@storybook/react';
import { PaginationDots } from './pagination-dots';

const meta = {
  title: 'Molecules/PaginationDots',
  component: PaginationDots,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
          Componente de paginação em formato de pontos.
          
          ### Características
          - Indicador visual da página atual
          - Clicável para navegação direta
          - Responsivo
          - Acessível via teclado
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    totalPages: {
      description: 'Número total de páginas',
      control: { type: 'number', min: 1, max: 10 },
    },
    currentPage: {
      description: 'Página atual (0-based)',
      control: { type: 'number', min: 0 },
    },
    onPageChange: {
      description: 'Callback chamado ao mudar de página',
      control: false,
    },
    className: {
      description: 'Classes CSS adicionais',
      control: 'text',
    },
  },
} satisfies Meta<typeof PaginationDots>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    totalPages: 5,
    currentPage: 0,
    onPageChange: (page) => console.log(`Página selecionada: ${page}`),
  },
};

export const ManyPages: Story = {
  args: {
    totalPages: 10,
    currentPage: 4,
    onPageChange: (page) => console.log(`Página selecionada: ${page}`),
  },
};

export const SinglePage: Story = {
  args: {
    totalPages: 1,
    currentPage: 0,
    onPageChange: (page) => console.log(`Página selecionada: ${page}`),
  },
};

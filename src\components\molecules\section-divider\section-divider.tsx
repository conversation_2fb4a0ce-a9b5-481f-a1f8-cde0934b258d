import { cn } from '../../../utils/class-utils';
import { Divider, Text } from '../../atoms';

interface SectionDividerProps {
  children: React.ReactNode;
  className?: string;
}

export function SectionDivider({ children, className }: SectionDividerProps) {
  return (
    <div
      className={cn(
        `fh-section__divider carousel-container flex items-center text-center gap-5 
      sm:px-[2.5rem] md:px-[3.75rem] lg:px-[4.5rem] xl:px-[4.45rem] 2xl:px-[5.75rem]`,
        className
      )}
    >
      <Divider className="min-w-[15%]" />
      <Text as="h2" size="xl" weight="semibold" className="fh-section__divider__text">
        {children}
      </Text>
      <Divider className="min-w-[15%]" />
    </div>
  );
}

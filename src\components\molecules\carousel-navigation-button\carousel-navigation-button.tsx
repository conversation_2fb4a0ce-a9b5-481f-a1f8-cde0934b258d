import { baseStyles, iconStyles, stateStyles } from './carousel-navigation-button.styles';
import { Button } from '../../atoms';
import { ArrowIcon } from '../../icons';
import { cn } from '@/utils/class-utils';

interface CarouselNavigationButtonProps {
  direction: 'left' | 'right';
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
  disabled: boolean;
  className?: string;
}

export function CarouselNavigationButton({ direction, onClick, disabled, className }: CarouselNavigationButtonProps) {
  return (
    <Button
      variant="blank"
      icon={
        <ArrowIcon
          className={cn(direction === 'right' && iconStyles.positionRight)}
          color={disabled ? iconStyles.disabled : iconStyles.default}
          size={22}
        />
      }
      onClick={onClick}
      aria-label={`${direction === 'left' ? 'Previous' : 'Next'} products`}
      className={cn(baseStyles, disabled && stateStyles.disabled, className)}
      disabled={disabled}
    />
  );
}

import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Divider } from './divider';

const meta = {
  title: 'Atoms/Divider',
  component: Divider,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="flex w-96">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    className: {
      description: 'Additional CSS classes to apply',
      control: 'text',
    },
  },
} satisfies Meta<typeof Divider>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const CustomColor: Story = {
  args: {
    className: 'bg-blue-500',
  },
};

export const CustomThickness: Story = {
  args: {
    className: 'h-[2px]',
  },
};

export const WithContent: Story = {
  render: () => (
    <div className="flex items-center gap-4 w-full">
      <Divider />
    </div>
  ),
};

export const CustomOpacity: Story = {
  args: {
    className: 'bg-black/30',
  },
};

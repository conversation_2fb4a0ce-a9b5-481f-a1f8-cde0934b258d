import type { Meta, StoryObj } from '@storybook/react';
import { ProductsSkeleton } from './products-skeleton';

const meta = {
  title: 'Molecules/ProductsSkeleton',
  component: ProductsSkeleton,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    type: {
      control: {
        type: 'select',
        options: ['similar', 'complementary'],
      },
      description: 'Type of skeleton cards to display',
    },
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="w-screen">
        <div className="flex flex-row gap-5 overflow-x-auto overflow-y-hidden w-full">
          <Story />
        </div>
      </div>
    ),
  ],
} satisfies Meta<typeof ProductsSkeleton>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Similar: Story = {
  args: {
    type: 'similar',
  },
  parameters: {
    docs: {
      description: {
        story:
          'Skeleton cards for similar products. The skeletons are displayed in a layout with one column, simulating the appearance of product cards while the actual content is loading.',
      },
    },
  },
};

export const Complementary: Story = {
  args: {
    type: 'complementary',
  },
  parameters: {
    docs: {
      description: {
        story:
          'Skeleton cards for complementary products. The skeletons are displayed in a layout with two columns, simulating the appearance of product cards while the actual content is loading.',
      },
    },
  },
};

import type { Meta, StoryObj } from '@storybook/react';
import { InputBase } from '.';

const meta = {
  title: 'Atoms/InputBase',
  component: InputBase,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    isError: {
      description: 'Whether the input is in an error state',
      control: 'boolean',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    fullWidth: {
      description: 'Whether the input takes up the full width of its container',
      control: 'boolean',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    placeholder: {
      description: 'Placeholder text for the input',
      control: 'text',
    },
    disabled: {
      description: 'Whether the input is disabled',
      control: 'boolean',
      table: {
        defaultValue: { summary: 'false' },
      },
    },
    className: {
      description: 'Additional CSS classes to apply',
      control: 'text',
    },
  },
} satisfies Meta<typeof InputBase>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: 'Digite algo...',
  },
};

export const WithError: Story = {
  args: {
    placeholder: 'Digite algo...',
    isError: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: 'Campo desabilitado',
    disabled: true,
  },
};

export const FullWidth: Story = {
  args: {
    placeholder: 'Input com largura total',
    fullWidth: true,
  },
};

export const States: Story = {
  render: () => (
    <div className="space-y-4 w-[300px]">
      <InputBase placeholder="Input padrão" />
      <InputBase placeholder="Input com erro" isError={true} />
      <InputBase placeholder="Input desabilitado" disabled />
      <InputBase placeholder="Input com largura total" fullWidth />
    </div>
  ),
};

import React, { createContext, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { PopoverContentProps, PopoverContextProps, PopoverProps, PopoverTriggerProps } from './popover.types';
import { cn } from '../../../utils/class-utils';
import { calculatePosition } from '../../../utils/popover-utils';
import { Slot } from '@/components/atoms';

const PopoverStyles = {
  base: 'relative',
  content: {
    base: 'z-[100] overflow-hidden rounded-md border border-[#EBEBEB] bg-white shadow-sm outline-none',
  },
};

const PopoverContext = createContext<PopoverContextProps | undefined>(undefined);

function usePopoverContext() {
  const context = useContext(PopoverContext);
  if (!context) {
    throw new Error('Popover components must be used within a Popover');
  }
  return context;
}

export function Popover({
  children,
  defaultOpen = false,
  open: controlledOpen,
  onOpenChange,
  className,
}: PopoverProps) {
  const [uncontrolledOpen, setUncontrolledOpen] = useState(defaultOpen);

  const open = controlledOpen !== undefined ? controlledOpen : uncontrolledOpen;
  const setOpen = useCallback(
    (newOpen: boolean) => {
      setUncontrolledOpen(newOpen);
      onOpenChange?.(newOpen);
    },
    [onOpenChange]
  );

  const triggerRef = useRef<HTMLElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  return (
    <PopoverContext.Provider value={{ open, setOpen, triggerRef, contentRef }}>
      <div className={cn(PopoverStyles.base, className)}>{children}</div>
    </PopoverContext.Provider>
  );
}

export function PopoverTrigger({
  children,
  disabled = false,
  className,
  asChild = false,
  ...props
}: PopoverTriggerProps) {
  const { setOpen, triggerRef, open } = usePopoverContext();

  const handleClick = (e: React.MouseEvent<HTMLElement>) => {
    if (disabled) return;
    e.preventDefault();
    setOpen(!open);
    props.onClick?.(e);
  };

  const Comp = asChild ? Slot : 'button';

  return (
    <div ref={triggerRef as React.RefObject<HTMLDivElement>} className="contents" data-state={open ? 'open' : 'closed'}>
      <Comp
        onClick={handleClick}
        aria-expanded={open}
        className={className}
        data-state={open ? 'open' : 'closed'}
        {...props}
      >
        {children}
      </Comp>
    </div>
  );
}

export function PopoverContent({
  children,
  position = 'bottom',
  align = 'center',
  sideOffset = 4,
  alignOffset = 0,
  className,
  closeOnOutsideClick = true,
  ...props
}: PopoverContentProps) {
  const { open, setOpen, triggerRef, contentRef } = usePopoverContext();
  const [contentPosition, setContentPosition] = useState({ top: 0, left: 0, position });

  const updatePosition = useCallback(() => {
    if (!open || !triggerRef.current || !contentRef.current) return;

    let triggerElement = triggerRef.current;

    if (triggerElement.classList.contains('contents')) {
      const realTrigger = triggerElement.querySelector('[data-state]');
      if (realTrigger) {
        triggerElement = realTrigger as HTMLElement;
      }
    }

    const triggerRect = triggerElement.getBoundingClientRect();
    const contentRect = contentRef.current.getBoundingClientRect();

    const scrollContainer = triggerElement.closest('.carousel-container, .carousel-container-complementary');
    const scrollLeft = scrollContainer ? (scrollContainer as HTMLElement).scrollLeft : 0;
    const scrollTop = scrollContainer ? (scrollContainer as HTMLElement).scrollTop : 0;

    const newPosition = calculatePosition(triggerRect, contentRect, position, align, sideOffset, alignOffset);

    setContentPosition({
      ...newPosition,
      left: newPosition.left - scrollLeft,
      top: triggerRect.bottom - scrollTop + sideOffset,
    });
  }, [open, position, align, sideOffset, alignOffset, triggerRef, contentRef]);

  useEffect(() => {
    if (!open) return;

    updatePosition();
    window.addEventListener('resize', updatePosition);
    window.addEventListener('scroll', updatePosition, true);

    return () => {
      window.removeEventListener('resize', updatePosition);
      window.removeEventListener('scroll', updatePosition, true);
    };
  }, [open, updatePosition]);

  useEffect(() => {
    if (!closeOnOutsideClick) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      let clickedOnTrigger = triggerRef.current?.contains(target);

      if (!clickedOnTrigger && triggerRef.current?.classList.contains('contents')) {
        const slotElements = document.querySelectorAll('[data-state]');
        for (const el of slotElements) {
          if (el.contains(target)) {
            clickedOnTrigger = true;
            break;
          }
        }
      }

      const clickedInsideContent = contentRef.current?.contains(target);

      if (!clickedInsideContent && !clickedOnTrigger && open) {
        setOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [open, setOpen, closeOnOutsideClick, triggerRef, contentRef]);

  if (!open) return null;

  return (
    <div
      ref={contentRef}
      className={cn(PopoverStyles.content.base, className)}
      style={{
        position: 'fixed',
        top: `${contentPosition.top}px`,
        left: `${contentPosition.left}px`,
      }}
      {...props}
    >
      {children}
    </div>
  );
}

export function PopoverClose({ children, className, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) {
  const { setOpen } = usePopoverContext();

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    setOpen(false);
    props.onClick?.(e);
  };

  return (
    <button className={className} onClick={handleClick} {...props}>
      {children}
    </button>
  );
}

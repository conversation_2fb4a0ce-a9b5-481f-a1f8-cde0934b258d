import { convertCmToFtIn, convertFtInToCm, convertKgToLb, convertLbToKg } from '@/utils/measurement-conversions';
import { useForm, UseFormReset, UseFormSetValue } from 'react-hook-form';
import { GENDER_OPTIONS } from '../constants/pocket-virtual-fitting-room';
import { BodyMeasures, PocketVFRFormData } from '../types/pocket-virtual-fitting-room';

export interface UnitySytem {
  setValue: UseFormSetValue<PocketVFRFormData>;
  isMetric: boolean;
  fields: {
    height?: string;
    heightIn?: string;
    heightFt?: string;
    weight?: string;
  };
}

const defaultValues: PocketVFRFormData = {
  gender: GENDER_OPTIONS.FEMALE,
  isMetric: true,
  bodyMeasures: {
    height: '',
    heightIn: '',
    heightFt: '',
    weight: '',
    age: '',
    bodyShapeChest: '3',
    bodyShapeWaist: '3',
    bodyShapeHip: '3',
  },
};

export function usePocketVFRForm() {
  const methods = useForm<PocketVFRFormData>({
    mode: 'onSubmit',
    defaultValues: defaultValues,
  });

  const { register, getValues, setValue, watch, handleSubmit } = methods;

  return {
    methods,
    handleSubmit,
    getValues,
    watch,
    register,
    setValue,
  };
}

export function getFieldName(name: string): keyof PocketVFRFormData | `bodyMeasures.${keyof BodyMeasures}` {
  if (name === 'gender' || name === 'isMetric') {
    return name as keyof PocketVFRFormData;
  }
  return `bodyMeasures.${name}` as `bodyMeasures.${keyof BodyMeasures}`;
}

export function changeUnitySystem({ setValue, isMetric, fields }: UnitySytem) {
  setValue('isMetric', !isMetric);

  const { height, heightIn, heightFt, weight } = fields;

  if (weight) {
    const newWeight = isMetric ? convertKgToLb(Number(weight)).toString() : convertLbToKg(Number(weight)).toString();
    setValue('bodyMeasures.weight', newWeight);
  }
  if ((heightFt ?? heightIn) || isMetric) {
    const newHeight = convertFtInToCm(Number(heightFt), Number(heightIn)).toString();
    setValue('bodyMeasures.height', newHeight !== '0' ? newHeight : '');
  }
  if (height || !isMetric) {
    const { ft, inches } = convertCmToFtIn(Number(height));
    setValue('bodyMeasures.heightFt', ft !== 0 || inches !== 0 ? ft.toString() : '');
    setValue('bodyMeasures.heightIn', ft !== 0 || inches !== 0 ? inches.toString() : '');
  }
}

export function getWatchedData(watch: (data: PocketVFRFormData) => PocketVFRFormData) {
  return watch({ ...defaultValues });
}

export function deleteBodyMeasures(reset: UseFormReset<PocketVFRFormData>, bodyMeasures: BodyMeasures) {
  reset({
    bodyMeasures: {
      ...bodyMeasures,
      height: '',
      heightIn: '',
      heightFt: '',
      weight: '',
      age: '',
      bodyShapeChest: '',
      bodyShapeWaist: '',
      bodyShapeHip: '',
    },
  });
}

{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "preview:test": "vite preview --port 3000", "prepare": "husky", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "cy:open": "cypress open", "cy:run": "cypress run", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "coverage": "nyc report --reporter=text-summary", "format": "prettier . --write"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@tanstack/react-query": "^5.66.0", "axios": "^1.8.4", "clsx": "^2.1.1", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "js-cookie": "^3.0.5", "react": "^18.3.0", "react-dom": "^18.3.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.56.1", "react-i18next": "^15.4.1", "react-router-dom": "^7.5.0", "sizebay-core-sdk": "^1.9.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.4", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@cypress/code-coverage": "^3.13.11", "@eslint/js": "^9.19.0", "@storybook/addon-essentials": "^8.5.3", "@storybook/addon-interactions": "^8.5.3", "@storybook/addon-onboarding": "^8.5.3", "@storybook/blocks": "^8.5.3", "@storybook/react": "^8.5.3", "@storybook/react-vite": "^8.5.3", "@storybook/test": "^8.5.3", "@tailwindcss/aspect-ratio": "^0.4.2", "@tanstack/eslint-plugin-query": "^5.66.0", "@testing-library/cypress": "^10.0.1", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.0.0", "@types/js-cookie": "^3.0.6", "@types/react": "18.3.2", "@types/react-dom": "18.3.2", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "3.1.1", "autoprefixer": "10.4.17", "cypress": "^14.0.2", "cypress-axe": "^1.5.0", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "eslint-plugin-storybook": "^0.11.2", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.14.0", "husky": "^9.1.7", "jsdom": "^24.0.0", "lint-staged": "^15.4.3", "nyc": "10.2.2-candidate.3", "prettier": "^3.4.2", "storybook": "^8.5.3", "tailwindcss": "3.4.1", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vite-plugin-istanbul": "^6.0.2", "vitest": "^3.1.1"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "pnpm": {"onlyBuiltDependencies": ["cypress", "esbuild"]}, "nyc": {"reporter": ["text-summary", "lcov"], "all": true, "exclude": ["**/*.test.ts", "**/*.test.tsx"]}, "packageManager": "pnpm@10.4.1+sha512.c753b6c3ad7afa13af388fa6d808035a008e30ea9993f58c6663e2bc5ff21679aa834db094987129aa4d488b86df57f7b634981b2f827cdcacc698cc0cfb88af"}
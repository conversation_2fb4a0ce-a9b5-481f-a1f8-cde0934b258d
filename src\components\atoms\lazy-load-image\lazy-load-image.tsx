import { memo, useRef, useState } from 'react';
import { CircularLoading } from '../circular-loading';
import { cn } from '@/utils/class-utils';

interface LazyLoadImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  className?: string;
  fallbackSrc?: string;
  loadingClassName?: string;
}

const CircularLoadingStyles = {
  container: 'relative w-full aspect-[4/3',
  overlay: 'absolute inset-0 flex items-center justify-center backdrop-blur-lg bg-white rounded-lg',
};

const imageCache = new Map<string, string>();

function autoCropImage(img: HTMLImageElement): Promise<string> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    const w = img.naturalWidth;
    const h = img.naturalHeight;

    canvas.width = w;
    canvas.height = h;
    ctx.drawImage(img, 0, 0);

    const imageData = ctx.getImageData(0, 0, w, h);
    const pixels = imageData.data;

    let top = 0,
      bottom = h,
      left = 0,
      right = w;

    const isWhite = (i: number) => pixels[i] === 255 && pixels[i + 1] === 255 && pixels[i + 2] === 255;

    outerTop: for (let y = 0; y < h; y++) {
      for (let x = 0; x < w; x++) {
        const i = (y * w + x) * 4;
        if (!isWhite(i)) {
          top = y;
          break outerTop;
        }
      }
    }

    outerBottom: for (let y = h - 1; y >= 0; y--) {
      for (let x = 0; x < w; x++) {
        const i = (y * w + x) * 4;
        if (!isWhite(i)) {
          bottom = y;
          break outerBottom;
        }
      }
    }

    outerLeft: for (let x = 0; x < w; x++) {
      for (let y = top; y < bottom; y++) {
        const i = (y * w + x) * 4;
        if (!isWhite(i)) {
          left = x;
          break outerLeft;
        }
      }
    }

    outerRight: for (let x = w - 1; x >= 0; x--) {
      for (let y = top; y < bottom; y++) {
        const i = (y * w + x) * 4;
        if (!isWhite(i)) {
          right = x;
          break outerRight;
        }
      }
    }

    const cropWidth = right - left;
    const cropHeight = bottom - top;

    const croppedCanvas = document.createElement('canvas');
    const croppedCtx = croppedCanvas.getContext('2d')!;
    croppedCanvas.width = cropWidth;
    croppedCanvas.height = cropHeight;
    croppedCtx.drawImage(canvas, left, top, cropWidth, cropHeight, 0, 0, cropWidth, cropHeight);

    resolve(croppedCanvas.toDataURL());
  });
}

export const LazyLoadImage = memo(({ className = '', loadingClassName = '', src, ...props }: LazyLoadImageProps) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [croppedSrc, setCroppedSrc] = useState<string | null>(null);
  const imgRef = useRef<HTMLImageElement>(null);

  const handleImageLoad = () => {
    if (!imgRef.current || croppedSrc || error) return;

    const cachedImage = imageCache.get(src);
    if (cachedImage) {
      setCroppedSrc(cachedImage);
      setLoading(false);
      return;
    }

    autoCropImage(imgRef.current).then((croppedImage) => {
      imageCache.set(src, croppedImage);
      setCroppedSrc(croppedImage);
      setLoading(false);
    });
  };

  const handleError = () => {
    setError(true);
  };

  if (error) {
    return <img src={src} alt={props.alt || 'fallback'} className={className} />;
  }

  return (
    <>
      <img
        ref={imgRef}
        src={croppedSrc || src}
        alt={props.alt || 'Image'}
        crossOrigin="anonymous"
        className={cn({ ['h-0 w-0']: loading }, { [className]: !loading })}
        onLoad={handleImageLoad}
        onError={handleError}
        {...props}
      />
      {loading && !croppedSrc && (
        <div className={cn(CircularLoadingStyles.container, loadingClassName)}>
          <div className={CircularLoadingStyles.overlay}>
            <CircularLoading />
          </div>
        </div>
      )}
    </>
  );
});

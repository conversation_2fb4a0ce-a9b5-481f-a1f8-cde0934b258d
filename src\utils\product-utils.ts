import { sortComplementaryProductsByClothType } from './carousel-utils';
import { ProductCardType } from '@/types/carousel';
import { ComplementaryProduct, ComplementaryProducts, ComplementaryProductsBundle } from '@/types/products';

/**
 * Transforms complementary products into a bundle format
 * @param complementaryProducts - Products to be complemented
 * @param baseProduct - Base product to be added to each bundle
 * @returns ComplementaryProductsBundle - Array of product bundles
 */
export const transformComplementaryProductsToBundle = (
  complementaryProducts: ComplementaryProducts['complementary'],
  baseProduct: ComplementaryProduct
): ComplementaryProductsBundle => {
  return complementaryProducts.map(({ first, second }) => {
    const bundle = [
      { ...baseProduct, itemType: ProductCardType.BaseProduct },
      { ...first, itemType: ProductCardType.FirstProduct },
    ];
    if (second) bundle.push({ ...second, itemType: ProductCardType.SecondaryProduct });
    return sortComplementaryProductsByClothType(bundle as ComplementaryProduct[]);
  }) as ComplementaryProductsBundle;
};

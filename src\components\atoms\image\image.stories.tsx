import type { Meta, StoryObj } from '@storybook/react';
import { Image } from './image';
import placeholder from '@/assets/one-piece.jpg';

const meta = {
  title: 'Atoms/Image',
  component: Image,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    src: {
      description: 'The source URL of the image',
      control: 'text',
      table: {
        type: { summary: 'string' },
      },
    },
    alt: {
      description: 'The alt text for the image',
      control: 'text',
      table: {
        type: { summary: 'string' },
      },
    },
    className: {
      description: 'Additional CSS classes to apply',
      control: 'text',
    },
  },
} satisfies Meta<typeof Image>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    src: `${placeholder}`,
    alt: 'Placeholder image',
  },
};

export const WithCustomClass: Story = {
  args: {
    src: `${placeholder}`,
    alt: 'Image with custom class',
    className: 'w-[290px] h-[328px] rounded-lg shadow-lg',
  },
};

interface CloseIconProps {
  dimensions?: number;
  viweBox?: string;
  className?: string;
}

function CloseIcon({ dimensions = 12, viweBox = '0 0 20 20', className }: CloseIconProps) {
  return (
    <svg
      width={dimensions}
      height={dimensions}
      viewBox={viweBox}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M18 6L6 18M6 6L18 18"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default CloseIcon;

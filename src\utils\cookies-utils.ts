export function getCookie(name: string): string | null {
  const cookiePattern = new RegExp('(^|;)\\s*' + name + '\\s*=\\s*' + '([^;]+)');
  const match = document.cookie.match(cookiePattern);
  return match ? match[2] : null;
}

export function setCookie(name: string, value: string) {
  const domain = '; domain=' + window.location.hostname.replace('www.', '.');
  const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toUTCString();
  document.cookie = `${encodeURIComponent(name)}=${encodeURIComponent(value)}; expires=${expires}; path=/${domain};`;
}

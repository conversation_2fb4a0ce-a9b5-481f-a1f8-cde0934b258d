import { useTranslation } from 'react-i18next';
import { Button, Text } from '@/components/atoms';
import { RootLayout } from '@/layouts';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function Fallback({ resetErrorBoundary }: { resetErrorBoundary: (...args: any[]) => void }) {
  const { t } = useTranslation();
  return (
    <section className="flex flex-col gap-2">
      <RootLayout>
        <div className="w-full py-12 flex flex-col items-center justify-center gap-5">
          <Text
            as="div"
            size="md"
            color="black"
            weight="regular"
            className="text-center"
            dangerouslySetInnerHTML={{ __html: t('fashion_hint.error.something_wrong') }}
          />
          <Button variant="primary" onClick={resetErrorBoundary}>
            {t('generics.try_again')}
          </Button>
        </div>
      </RootLayout>
    </section>
  );
}

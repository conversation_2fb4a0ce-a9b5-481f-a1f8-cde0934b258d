import { t } from 'i18next';
import { ChangeEvent, useCallback, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { Button, Text, ToggleGroup, ToggleGroupItem } from '@/components/atoms';
import { Step } from '@/components/atoms/step';
import { FormInput } from '@/components/molecules';
import { GENDER_OPTIONS } from '@/constants/pocket-virtual-fitting-room';
import { changeUnitySystem, getWatchedData } from '@/hooks/use-pocket-vfr-form';
import { useGlobalContext } from '@/store/global';
import {
  BodyMeasures,
  ClothingStep,
  Gender,
  GenericStep,
  PocketVFRFormData,
} from '@/types/pocket-virtual-fitting-room';
import { cn } from '@/utils/class-utils';

interface BodyMeasuresStepProps {
  isSmallerDevices: boolean;
}

const BodyMeasuresStyles = {
  root: {
    base: 'flex flex-col gap-4',
    mobile: 'gap-4',
  },
  content: {
    container: {
      base: 'flex flex-col gap-2',
      mobile: 'gap-2',
    },
  },
  toggle: {
    container: 'gap-2',
    item: {
      base: 'text-[#272727] font-normal',
      selected: 'font-semibold',
    },
  },
};

export const BodyMeasuresStep = ({ isSmallerDevices }: BodyMeasuresStepProps) => {
  const {
    actions: { setCurrentStep },
  } = useGlobalContext();

  const { setValue, watch } = useFormContext<PocketVFRFormData>();

  const { isMetric, bodyMeasures, gender } = getWatchedData(watch);
  const { height, heightIn, heightFt, weight, age } = bodyMeasures || {};

  const handleInputChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      let name = e.target.name as keyof PocketVFRFormData | `bodyMeasures.${keyof BodyMeasures}`;
      if (name !== 'gender' && name !== 'isMetric') {
        name = `bodyMeasures.${name}` as `bodyMeasures.${keyof BodyMeasures}`;
      }
      setValue(name, e.target.value);
    },
    [setValue]
  );

  const enableNextButton = useMemo(() => {
    return (height || (heightFt && heightIn)) && weight && age;
  }, [height, heightFt, heightIn, weight, age]);

  const handleUnitSystemChange = useCallback(() => {
    changeUnitySystem({
      setValue,
      isMetric,
      fields: {
        height,
        heightIn,
        heightFt,
        weight,
      },
    });
  }, [setValue, isMetric, height, heightFt, heightIn, weight]);

  return (
    <Step.Root className={cn(BodyMeasuresStyles.root.base)}>
      <Step.Header className={cn('flex flex-col', { 'gap-3': isSmallerDevices })}>
        {isSmallerDevices && (
          <Text size="md" weight="medium" className={'fh-form__title text-center'}>
            {t('pocket_vfr.introduction.caption')}
          </Text>
        )}
        <ToggleGroup
          type="single"
          onValueChange={(value) => setValue('gender', value as Gender)}
          value={gender as Gender}
          className="gap-2"
        >
          <ToggleGroupItem
            value={GENDER_OPTIONS.FEMALE}
            className={cn(BodyMeasuresStyles.toggle.item.base, {
              [BodyMeasuresStyles.toggle.item.selected]: gender === GENDER_OPTIONS.FEMALE,
            })}
            fullWidth
          >
            {t('pocket_vfr.gender.female')}
          </ToggleGroupItem>
          <ToggleGroupItem
            value={GENDER_OPTIONS.MALE}
            className={cn(BodyMeasuresStyles.toggle.item.base, {
              [BodyMeasuresStyles.toggle.item.selected]: gender === GENDER_OPTIONS.MALE,
            })}
            fullWidth
          >
            {t('pocket_vfr.gender.male')}
          </ToggleGroupItem>
        </ToggleGroup>
      </Step.Header>
      <Step.Content className={cn(BodyMeasuresStyles.content.container.base)}>
        <FormInput
          label={t('pocket_vfr.measures.height')}
          type="height"
          defaultValue={isMetric ? height || '' : heightFt || ''}
          secondaryValue={heightIn}
          isMetric={isMetric}
          handleChange={handleInputChange}
          onToggleChange={handleUnitSystemChange}
          metricUnit="cm"
          imperialUnit="in"
        />
        <FormInput
          label={t('pocket_vfr.measures.weight')}
          type="weight"
          defaultValue={weight as string}
          isMetric={isMetric}
          handleChange={handleInputChange}
          onToggleChange={handleUnitSystemChange}
          metricUnit="kg"
          imperialUnit="lb"
        />
        <FormInput
          label={t('pocket_vfr.measures.age')}
          type="age"
          defaultValue={age as string}
          helperText={t('pocket_vfr.measures.years')}
          handleChange={handleInputChange}
        />
      </Step.Content>
      <Step.Footer>
        <Button
          type="button"
          fullWidth
          variant="secondary"
          size="sm"
          onClick={() => setCurrentStep(GenericStep.SELECT_CATEGORY)}
        >
          {t('pocket_vfr.action_button.go_back')}
        </Button>
        <Button
          type="button"
          disabled={!enableNextButton}
          fullWidth
          size="sm"
          onClick={() => setCurrentStep(ClothingStep.BODY_SHAPE)}
        >
          {t('pocket_vfr.action_button.next')}
        </Button>
      </Step.Footer>
    </Step.Root>
  );
};

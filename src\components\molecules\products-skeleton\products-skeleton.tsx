import { Fragment } from 'react/jsx-runtime';
import { Divider, Skeleton } from '../../atoms';
import { DeviceType, useDevice } from '@/hooks/use-device';
import { CarouselVariant } from '@/types/carousel';
import { useItemsPerPage } from '@/utils/items-per-page-utils';

const ProductsSkeletonStyles = {
  generics: {
    itemTitle: 'h-5 w-4/4',
    itemPrice: 'h-5 w-2/4',
  },
  type: {
    similar: {
      container: 'flex-shrink-0 gap-2 flex flex-col carousel-card-height similar-carousel-card-width',
      image: 'h-full rounded-lg aspect-[4/5]',
    },
    complementary: {
      list: 'flex flex-col rounded-lg border border-[#D6D6D6] h-fit w-full gap-2',
      listContainer: 'flex flex-row gap-2 p-2 w-full',
      image: 'image-card-variant aspect-[4/4] w-1/2 image-card-variant-height rounded-lg',
      itemInfo: 'flex flex-col justify-center gap-2 w-full',
    },
  },
};

export function ProductsSkeleton({ type }: { type: CarouselVariant }) {
  const itemsPerPage = useItemsPerPage({
    itemsType: type,
  });
  const isMobile = useDevice(DeviceType.Mobile) as boolean;

  const skeletonCount =
    type === CarouselVariant.Similar ? (isMobile ? 2 : Math.max(itemsPerPage + 1, 4)) : itemsPerPage;

  const skeletons = Array.from({ length: skeletonCount }, (_, index) => index);

  const renderSkeletonCard = () =>
    type === CarouselVariant.Similar
      ? skeletons.map((index) => (
          <div key={index} className={ProductsSkeletonStyles.type[type].container}>
            <Skeleton className={ProductsSkeletonStyles.type[type].image} />
            <Skeleton className={ProductsSkeletonStyles.generics.itemTitle} />
            <Skeleton className={ProductsSkeletonStyles.generics.itemPrice} />
          </div>
        ))
      : skeletons.map((index) => (
          <div key={index} className={ProductsSkeletonStyles.type[type].list}>
            {[0, 1, 2].map((i) => (
              <Fragment key={i}>
                <div className={ProductsSkeletonStyles.type[type].listContainer}>
                  <Skeleton className={ProductsSkeletonStyles.type[type].image} />
                  <div className={ProductsSkeletonStyles.type[type].itemInfo}>
                    <Skeleton className={ProductsSkeletonStyles.generics.itemTitle} />
                    <Skeleton className={ProductsSkeletonStyles.generics.itemPrice} />
                  </div>
                </div>
                {i !== 2 && <Divider className="w-full flex-none" />}
              </Fragment>
            ))}
          </div>
        ));

  return <>{renderSkeletonCard()}</>;
}

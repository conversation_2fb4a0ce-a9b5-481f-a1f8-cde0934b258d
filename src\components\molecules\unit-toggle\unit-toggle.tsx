import React from 'react';
import { selectedLabelStyles, unitLabelStyles, unitToggleContainerStyles } from './unit-toggle.styles';
import { cn } from '../../../utils/class-utils';
import { Switch } from '../../atoms/switch';

interface UnitToggleProps {
  isMetric: boolean;
  onToggle: () => void;
  metricUnit: string;
  imperialUnit: string;
  className?: string;
}

export const UnitToggle: React.FC<UnitToggleProps> = ({ isMetric, onToggle, metricUnit, imperialUnit, className }) => {
  return (
    <div className={cn(unitToggleContainerStyles, className)}>
      <span className={cn('fh-unit__toggle__label', unitLabelStyles, isMetric && selectedLabelStyles)}>
        {metricUnit}
      </span>
      <Switch
        checked={!isMetric}
        onCheckedChange={onToggle}
        variant="primary"
        className={'m-auto translate-y-[2px] fh-unit__toggle'}
      />
      <span className={cn('fh-unit__toggle__label', unitLabelStyles, !isMetric && selectedLabelStyles)}>
        {imperialUnit}
      </span>
    </div>
  );
};

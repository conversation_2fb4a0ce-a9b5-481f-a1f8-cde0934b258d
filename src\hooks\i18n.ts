import i18n from 'i18next';
import HttpBackend, { HttpBackendOptions } from 'i18next-http-backend';
import { initReactI18next } from 'react-i18next';
import { getMappedLanguages } from '../utils/get-mapped-language';
import { getQueryParams } from '../utils/query-utils';

function getCurrentLanguage() {
  const qp = getQueryParams();
  const { value } = getMappedLanguages(qp.lang);

  return value.i18n;
}

i18n
  .use(HttpBackend)
  .use(initReactI18next)
  .init<HttpBackendOptions>({
    lng: getCurrentLanguage(),
    fallbackLng: getCurrentLanguage(),
    debug: false,
    load: 'languageOnly',
    backend: {
      loadPath: 'https://static.sizebay.technology/6f2d616c92264904a42337aaed5b2250/_latest/{{lng}}',
    },
  });

export default i18n;

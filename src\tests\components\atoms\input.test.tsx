import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi } from 'vitest';
import { Input } from '../../../components/atoms';

describe('Input', () => {
  describe('Rendering', () => {
    it('should render with default props', () => {
      render(<Input />);
      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
    });

    it('should render with base styles', () => {
      render(<Input />);
      const input = screen.getByRole('textbox');
      expect(input).toHaveClass('rounded-lg px-4 py-3 border border-[#272727] bg-white');
      expect(input).not.toHaveClass('w-full');
    });

    it('should render with placeholder', () => {
      render(<Input placeholder="Digite algo..." />);
      expect(screen.getByPlaceholderText('Digite algo...')).toBeInTheDocument();
    });
  });

  describe('States', () => {
    it('should handle error state', () => {
      render(<Input isError />);
      const input = screen.getByRole('textbox');
      expect(input).toHaveClass('border-[#FF4444]');
      expect(input).toHaveClass('focus:ring-[#FF4444]/20');
    });

    it('should handle disabled state', () => {
      render(<Input disabled />);
      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
      expect(input).toHaveClass('disabled:bg-[#F5F5F5] disabled:cursor-not-allowed disabled:border-[#AEAEAE]');
    });
  });

  describe('Interactions', () => {
    it('should handle onChange event', async () => {
      const handleChange = vi.fn();
      const user = userEvent.setup();

      render(<Input onChange={handleChange} />);
      const input = screen.getByRole('textbox');

      await user.type(input, 'test');
      expect(handleChange).toHaveBeenCalledTimes(4);
      expect(input).toHaveValue('test');
    });

    it('should handle focus and blur events', async () => {
      const handleFocus = vi.fn();
      const handleBlur = vi.fn();
      const user = userEvent.setup();

      render(<Input onFocus={handleFocus} onBlur={handleBlur} />);
      const input = screen.getByRole('textbox');

      await user.click(input);
      expect(handleFocus).toHaveBeenCalledTimes(1);

      await user.tab();
      expect(handleBlur).toHaveBeenCalledTimes(1);
    });
  });

  describe('Styling', () => {
    it('should apply full width style when fullWidth prop is true', () => {
      render(<Input fullWidth />);
      expect(screen.getByRole('textbox')).toHaveClass('w-full');
    });

    it('should accept and apply custom className', () => {
      render(<Input className="custom-class" />);
      expect(screen.getByRole('textbox')).toHaveClass('custom-class');
    });

    it('should maintain base styles when custom className is provided', () => {
      render(<Input className="custom-class" />);
      const input = screen.getByRole('textbox');

      expect(input).toHaveClass('custom-class');
      expect(input).toHaveClass('rounded-lg');
      expect(input).toHaveClass('px-4');
      expect(input).toHaveClass('py-3');
    });
  });

  describe('Validation', () => {
    it('should handle required attribute', () => {
      render(<Input required />);
      expect(screen.getByRole('textbox')).toHaveAttribute('required');
    });

    it('should handle maxLength attribute', () => {
      render(<Input maxLength={10} />);
      expect(screen.getByRole('textbox')).toHaveAttribute('maxLength', '10');
    });

    it('should handle type attribute', () => {
      render(<Input type="email" />);
      expect(screen.getByRole('textbox')).toHaveAttribute('type', 'email');
    });
  });

  describe('Accessibility', () => {
    it('should handle aria-label attribute', () => {
      render(<Input aria-label="Email input" />);
      expect(screen.getByRole('textbox')).toHaveAttribute('aria-label', 'Email input');
    });

    it('should handle aria-invalid attribute when in error state', () => {
      render(<Input isError />);
      expect(screen.getByRole('textbox')).toHaveAttribute('aria-invalid', 'true');
    });
  });
});

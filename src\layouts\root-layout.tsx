import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Outlet, useLocation } from 'react-router-dom';
import { cn } from '@/utils/class-utils';
import { SectionDivider } from '@/components/molecules';

export function RootLayout({ children }: { children?: React.ReactNode }) {
  const { t } = useTranslation();
  const location = useLocation();

  const SectionTitles = useMemo(
    () => ({
      '/complementary': t('fashion_hint.complementary_products.description'),
      '/similar': t('fashion_hint.similar_products.description'),
    }),
    [t]
  );

  return (
    <section
      className={cn('flex flex-col gap-2', {
        'fh-complementary': location.pathname === '/complementary',
        'fh-similar': location.pathname === '/similar',
      })}
    >
      <SectionDivider className="wrap self-center">
        {SectionTitles[location.pathname as keyof typeof SectionTitles]}
      </SectionDivider>
      <section className="flex flex-col gap-2">{children ? children : <Outlet />}</section>
    </section>
  );
}

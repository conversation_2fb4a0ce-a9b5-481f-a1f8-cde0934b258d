import React from 'react';

export type PopoverPosition = 'top' | 'right' | 'bottom' | 'left';
export type PopoverAlign = 'start' | 'center' | 'end';

export interface PopoverContextProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  triggerRef: React.RefObject<HTMLElement>;
  contentRef: React.RefObject<HTMLDivElement>;
}

export interface PopoverProps {
  children: React.ReactNode;
  defaultOpen?: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  className?: string;
}

export interface PopoverTriggerProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode;
  disabled?: boolean;
  className?: string;
  /** Whether to keep the trigger in the DOM when disabled */
  asChild?: boolean;
}

export interface PopoverContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  position?: PopoverPosition;
  align?: PopoverAlign;
  /** The side offset from the trigger */
  sideOffset?: number;
  /** The align offset */
  alignOffset?: number;
  className?: string;
  closeOnOutsideClick?: boolean;
}

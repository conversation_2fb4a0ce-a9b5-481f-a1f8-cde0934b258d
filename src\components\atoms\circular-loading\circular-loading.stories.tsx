import type { Meta, StoryObj } from '@storybook/react';
import { CircularLoading } from './circular-loading';

const meta = {
  title: 'Atoms/CircularLoading',
  component: CircularLoading,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    className: {
      description: 'Additional CSS classes to apply',
      control: 'text',
    },
  },
} satisfies Meta<typeof CircularLoading>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const Large: Story = {
  args: {
    className: 'w-12 h-12',
  },
};

export const Small: Story = {
  args: {
    className: 'w-4 h-4',
  },
};

export const CustomColor: Story = {
  args: {
    className: 'text-blue-500',
  },
};

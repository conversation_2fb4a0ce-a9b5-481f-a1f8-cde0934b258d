/**
 * Creates a debounced function that delays invoking the provided function
 * until after the specified wait time has elapsed since the last time it was invoked.
 *
 * @param callbackFunction - The function to debounce
 * @param debounceDelay - The number of milliseconds to delay
 * @returns A debounced version of the provided function
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  callbackFunction: T,
  debounceDelay: number
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout>;

  return function (...args: Parameters<T>): void {
    const invokeAfterTimeout = () => {
      callbackFunction(...args);
    };

    clearTimeout(timeoutId);
    timeoutId = setTimeout(invokeAfterTimeout, debounceDelay);
  };
}

import { useInfiniteQuery } from '@tanstack/react-query';
import { useEffect, useMemo } from 'react';
import { useQueryParams } from './use-query-params';
import { getSimilarProducts } from '../services/similar-products';
import { Product, SimilarProductsParams } from '../types/products';
import { getMappedLanguages } from '@/utils/get-mapped-language';
import { useGlobalContext } from '@/store/global';

const CACHE_TIME = 1000 * 60 * 5;
const STALE_TIME = 1000 * 60 * 2;

export function useSimilarProducts() {
  const { raw: params, isValid } = useQueryParams();
  const {
    state: { sessionId, personaHash },
    actions: { setPersonaHash, setPreviousSizeNotFound },
  } = useGlobalContext();

  const queryParams = useMemo(() => {
    const baseParams = { ...params, locale: getMappedLanguages(params.lang).value.backend } as SimilarProductsParams;

    if (sessionId) {
      baseParams.sid = sessionId;
    }

    if (personaHash) {
      baseParams.personaHash = personaHash;
      baseParams.filterByWhatFitsMe = true;
    }

    return baseParams;
  }, [params, sessionId, personaHash]);

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, error, refetch } = useInfiniteQuery({
    queryKey: ['similar-products', queryParams],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await getSimilarProducts({ ...queryParams, page: pageParam });
      return response;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      const { pageDetails } = lastPage;
      const nextPage = pageDetails.page + 1;
      const totalPages = Math.ceil(pageDetails.total / pageDetails.perPage);

      return nextPage <= totalPages ? nextPage : undefined;
    },
    enabled: isValid && (!!sessionId || !!params.sid),
    gcTime: CACHE_TIME,
    staleTime: STALE_TIME,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  useEffect(() => {
    if (data) {
      const allProducts = data.pages.flatMap((page) => page.products);
      if (!allProducts.length && personaHash) {
        setPersonaHash(null);
        setPreviousSizeNotFound(true);
        refetch();
      }
    }
  }, [data, refetch, personaHash, setPersonaHash, setPreviousSizeNotFound]);

  const products = useMemo(() => {
    if (!data) return [];

    return data.pages.reduce<Product[]>((acc, pageIdx) => {
      const {
        products,
        pageDetails: { page },
      } = pageIdx;
      return [...acc, ...products.map((product) => ({ ...product, page }))];
    }, []);
  }, [data]);

  return {
    data,
    products,
    isLoading,
    error,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  };
}

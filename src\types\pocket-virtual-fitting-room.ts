import { GENDER_OPTIONS, NAVIGATION_OPTIONS, TOGGLE_LABELS } from '@/constants/pocket-virtual-fitting-room';

export type Gender = (typeof GENDER_OPTIONS)[keyof typeof GENDER_OPTIONS];

export type Navigation = (typeof NAVIGATION_OPTIONS)[keyof typeof NAVIGATION_OPTIONS];

export type ToggleLabels = (typeof TOGGLE_LABELS)[keyof typeof TOGGLE_LABELS];

export type SizeGuideKeys =
  | 'gender'
  | 'isMetric'
  | 'age'
  | 'weight'
  | 'height'
  | 'heightFt'
  | 'heightIn'
  | 'bodyShapeChest'
  | 'bodyShapeWaist'
  | 'bodyShapeHip';

export type SizeGuideValues =
  | 'gender'
  | 'isMetric'
  | 'bodyMeasures.age'
  | 'bodyMeasures.weight'
  | 'bodyMeasures.height'
  | 'bodyMeasures.heightFt'
  | 'bodyMeasures.heightIn'
  | 'bodyMeasures.bodyShapeChest'
  | 'bodyMeasures.bodyShapeWaist'
  | 'bodyMeasures.bodyShapeHip';

export interface PocketVFRFormData {
  gender: Gender | undefined;
  isMetric: boolean;
  bodyMeasures?: BodyMeasures;
}

export interface BodyMeasures {
  height: string;
  heightIn: string;
  heightFt: string;
  weight: string;
  age: string;
  bodyShapeChest: string;
  bodyShapeWaist: string;
  bodyShapeHip: string;
}

export interface ToggleOption {
  value: Gender | Navigation;
  label: ToggleLabels;
}

export type ToggleOptions = {
  gender: ToggleOption[];
  navigation: ToggleOption[];
};

export enum GenericStep {
  SELECT_CATEGORY = 0,
}

export enum ClothingStep {
  BODY_MEASURES = 1,
  BODY_SHAPE = 2,
}

export enum ShoeStep {
  SHOE_MEASURES = 3,
  SHOE_SHAPE = 4,
  SHOE_KNOWN_MEASURE = 5,
}

export type PocketVFRStep = GenericStep | ClothingStep | ShoeStep;

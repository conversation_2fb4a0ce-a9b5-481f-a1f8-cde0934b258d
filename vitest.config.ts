import react from '@vitejs/plugin-react';
import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/tests/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'lcov', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.test.{ts,tsx}',
        '**/*.spec.{ts,tsx}',
        '**/*.stories.{ts,tsx}',
        '.storybook/**/*',
      ],
    },
    alias: {
      '@': resolve(__dirname, './src'),
    },
    testTimeout: 10000,
    silent: true,
    onConsoleLog: (log) => {
      if (log.includes('ReactDOMTestUtils.act is deprecated')) return false;
    },
  },
});

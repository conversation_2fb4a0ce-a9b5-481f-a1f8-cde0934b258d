import type { Meta, StoryObj } from '@storybook/react';
import { SectionDivider } from './section-divider';

const meta = {
  title: 'Molecules/SectionDivider',
  component: SectionDivider,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="w-[800px]">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    text: {
      description: 'Text to display in the section divider',
      control: 'text',
    },
    className: {
      description: 'Additional CSS classes to apply',
      control: 'text',
    },
  },
} satisfies Meta<typeof SectionDivider>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    text: 'Section Title',
  },
};

export const LongText: Story = {
  args: {
    text: 'This is a very long section title that might wrap to multiple lines',
  },
};

export const WithCustomStyling: Story = {
  args: {
    text: 'Custom Styled Divider',
    className: 'bg-blue-100 text-blue-800',
  },
};

export const WithCustomDividers: Story = {
  args: {
    text: 'Custom Dividers',
    className: '[&>hr]:bg-blue-500 [&>hr]:h-[2px]',
  },
};

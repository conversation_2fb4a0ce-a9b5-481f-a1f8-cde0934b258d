window.SizebayFashionHint = (() => {
  return {
    getTenantId: () => {
      return 6273;
    },

    getCollectionName: () => {
      return 'shopify-sizebay-develop';
    },

    getPermalink: () => {
      return 'https://32c6c6-a9.myshopify.com/products/renner-blusa-top-faixa-com-ziper-em-vinil-preto-4f8baf';
    },

    getLang: () => {
      return 'en';
    },

    getPerPage: () => {
      return 8;
    },

    getBaseVrfUrl() {
      return 'https://vfr-v3-production.sizebay.technology/';
      //return 'https://vfr-v3-staging.sizebay.eu/';
    },

    getBaseFashionHintUrl() {
      // return 'https://fashion-hint.internalsizebay.com/';
      //return 'http://************:3000/'
      return 'http://localhost:3000/';
    },

    getSizeSystem() {
      return 'US';
    },

    getEnvironment() {
      return 'local';
    },

    getCurrency() {
      return 'USD';
    },

    getDefaultUnit() {
      return 'in';
    },

    getSimilarContainerId() {
      return 'fashion-hint-similar';
    },

    getComplementaryContainerId() {
      return 'fashion-hint-complementary';
    },

    getSimilarityThreshold() {
      return 0.1;
    },
  };
})();

const insertScript = (src, base) => {
  const app = document.createElement('script');
  app.id = base ? 'szb-vfr__base' : 'szb-vfr__module';
  app.setAttribute('src', src);
  document.querySelector('head').appendChild(app);
};

const sizebayImplantation = () => {
  const environment = window.SizebayFashionHint.getEnvironment();
  const baseFashionHintUrl = window.SizebayFashionHint.getBaseFashionHintUrl();

  if (environment === 'local') {
    return insertScript('./index.js', true);
  }

  return insertScript(`${baseFashionHintUrl}implantation/index.js`, true);
};

function SizebayInit() {
  sizebayImplantation();
}

SizebayInit();
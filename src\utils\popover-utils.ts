type Position = {
  top: number;
  left: number;
  position: 'top' | 'right' | 'bottom' | 'left';
};

export function calculatePosition(
  triggerRect: DOMRect,
  contentRect: DOMRect,
  preferredSide: 'top' | 'right' | 'bottom' | 'left' = 'bottom',
  align: 'start' | 'center' | 'end' = 'center',
  sideOffset = 4,
  alignOffset = 0
): Position {
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;

  const position: Position = { top: 0, left: 0, position: preferredSide };

  if (preferredSide === 'bottom' || preferredSide === 'top') {
    switch (align) {
      case 'start':
        position.left = triggerRect.left + alignOffset;
        break;
      case 'end':
        position.left = triggerRect.right - contentRect.width - alignOffset;
        break;
      default:
        position.left = triggerRect.left + (triggerRect.width - contentRect.width) / 2;
    }

    if (preferredSide === 'bottom') {
      position.top = triggerRect.bottom + sideOffset;
    } else {
      position.top = triggerRect.top - contentRect.height - sideOffset;
    }
  }

  if (preferredSide === 'left' || preferredSide === 'right') {
    switch (align) {
      case 'start':
        position.top = triggerRect.top + alignOffset;
        break;
      case 'end':
        position.top = triggerRect.bottom - contentRect.height - alignOffset;
        break;
      default:
        position.top = triggerRect.top + (triggerRect.height - contentRect.height) / 2;
    }

    if (preferredSide === 'right') {
      position.left = triggerRect.right + sideOffset;
    } else {
      position.left = triggerRect.left - contentRect.width - sideOffset;
    }
  }

  position.top = Math.max(8, Math.min(position.top, viewportHeight - contentRect.height - 8));
  position.left = Math.max(8, Math.min(position.left, viewportWidth - contentRect.width - 8));

  return position;
}

# Fashion Hint - Similarity-Based Recommendations

**Fashion Hint** is a product recommendation application designed to increase sales and average order value on e-commerce platforms. The solution uses artificial intelligence to analyze product images and display visually similar items, providing a personalized experience for the customer.

### Key Features

- **Similar Products Carousel:** Displays a list of similar products on the **Product Detail Page (PDP)**.
- **Size Recommendation:** Displays a recommended size based on the user's information (height, weight, and age).
- **Responsive Carousel:** Optimized display for **desktop** and **mobile** devices.
- **API Integration:** Communicates with an external API to fetch recommendations and track events.
- **Event Tracking:** Records interactions such as product impressions, clicks, and add-to-cart actions.

## 🛠️ Technologies Used

- **React**
- **TypeScript**
- **TailwindCSS**
- **Storybook** for component documentation
- **Cypress** for integration and end-to-end testing
- **Vite** as the bundler
- **Eslint** and **Prettier** for code quality and formatting

## 🚀 Getting Started

Follow the steps below to run the project locally.

### Prerequisites

- **Node.js** v22.13.1
- **pnpm** v10.x

### Installing Dependencies

Clone the repository and install the dependencies:

```bash
git clone https://github.com/your-username/fashion-hint.git
cd fashion-hint
pnpm install
```

### Environment Variables

Copy the environment variables:

```bash
cp .env.example .env
```

### Running the Project

To run the application locally, execute:

```bash
pnpm dev
```

The application will be available at [http://localhost:3000](http://localhost:3000).

### Running Tests

To run the tests, use the following commands:

#### End-to-End and Integration Tests with Cypress:

```bash
pnpm cy:open
```

### Running Storybook

To view the components in **Storybook**, execute:

```bash
pnpm storybook
```

Storybook will be available at [http://localhost:6006](http://localhost:6006).

## 📁 Folder Structure

The project structure follows the **Atomic Design** pattern, ensuring reusable and scalable components. The main folders are:

- **`src/assets/`**: Static assets such as images and fonts.
- **`src/components/`**: Contains reusable components divided into **atoms**, **molecules**, **organisms**, **templates**, and **pages**.
- **`src/hooks/`**: Custom React hooks.
- **`src/store/`**: Global state management.
- **`src/stories/`**: Storybook stories for component documentation.
- **`src/services/`**: Functions for communication with external APIs.
- **`src/styles/`**: Global styles and theme configurations.
- **`src/types/`**: Type definitions.
- **`src/utils/`**: Utility functions and helpers.

## 📊 Event Tracking

The application records events to track user interactions:

- **Impressions**: Product data, pagination, product origin, and size recommendation.
- **Clicks**: Product data, the page on which the click occurred, and whether a size recommendation was provided.
- **AddToCart**: Marks if the product was clicked from a recommendation.
- **Order**: Marks if the product was added to the cart from a recommendation.

## 📜 Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm preview` - Preview production build
- `pnpm lint` - Run ESLint
- `pnpm lint:fix` - Fix ESLint issues
- `pnpm format` - Format code with Prettier
- `pnpm storybook` - Start Storybook server
- `pnpm cy:open` - Open Cypress test runner
- `pnpm cy:run` - Run Cypress tests headlessly
- `pnpm coverage` - View test coverage report

## 🔄 Git Workflow

1. Branch naming:

   - Feature: `feature/your-feature-name`
   - Bug fix: `fix/issue-description`
   - Release: `release/version-number`

2. Commit messages follow conventional commits standard

3. Pull requests:
   - Must pass all checks (tests, coverage, lint)
   - Require code review
   - Can only merge to main from release branch

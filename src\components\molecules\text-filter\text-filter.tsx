import { balloonBtnStyles, balloonContainerStyles } from './text-filter.styles';
import { cn } from '../../../utils/class-utils';
import { Button, Text } from '../../atoms';
import { CloseIcon } from '../../icons';

interface TextBalloonProps {
  text: string;
  onClose?: () => void;
  className?: string;
}

export function TextFilter({ text, onClose, className }: TextBalloonProps) {
  return (
    <div className={cn(balloonContainerStyles, className, 'fh-text__filter__container')}>
      <Text as="span" size="sm" color="black" className="fh-text__filter">
        {text}
      </Text>
      <Button variant="blank" onClick={onClose} className={balloonBtnStyles} aria-label="Close">
        <CloseIcon />
      </Button>
    </div>
  );
}

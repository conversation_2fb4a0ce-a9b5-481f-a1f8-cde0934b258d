import { sizebayClient } from '../config/sizebay-client';
import { SimilarProductsParams, SimilarProductsResponse } from '../types/products';

export async function getSimilarProducts(params: SimilarProductsParams): Promise<SimilarProductsResponse> {
  try {
    const response = await sizebayClient.getSimilarProducts({
      collectionName: params.collectionName,
      sid: params.sid,
      tenantId: params.tenantId,
      permalink: params.permalink,
      page: params.page,
      perPage: params.perPage,
      filterByWhatFitsMe: params.filterByWhatFitsMe,
      personaHash: params.personaHash,
      sizeSystem: params.sizeSystem ?? 'en',
      similarityThreshold: params.similarityThreshold,
      locale: params.locale,
      currency: params.currency,
    });

    if (response?.invalidPersonaHash) {
      window.parent.postMessage({ id: 'szb_reset_persona_hash' }, '*');
    }

    if (response.data.length === 0) {
      window.parent.postMessage({ id: 'szb_recommendation_not_found', type: 'similar' }, '*');
    }

    return {
      products: response.data,
      pageDetails: {
        page: response.page,
        perPage: response.perPage,
        total: response.total,
      },
    };
  } catch (error) {
    const statusCode = (error as { statusCode?: number }).statusCode;

    if (statusCode === 404) {
      window.parent.postMessage({ id: 'szb_recommendation_not_found', type: 'similar' }, '*');
    }

    return {
      products: [],
      pageDetails: {
        page: 1,
        perPage: 4,
        total: 4,
      },
    };
  }
}

/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef } from 'react';
import { FormProvider } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { StepManager } from '../step-manager';
import { Button, CircularLoading, Text } from '@/components/atoms';
import { CloseIcon } from '@/components/icons';
import { InfoCard } from '@/components/molecules';
import { usePocketVFRForm, usePostMessage } from '@/hooks';
import { DeviceType, useDevice } from '@/hooks/use-device';
import { useGlobalContext } from '@/store/global';
import { cn } from '@/utils/class-utils';

export interface PocketVFRProps {
  onClose?: () => void;
  resetRecommendation: () => void;
}

export const SizeRecommendationForm = ({ onClose = () => {}, resetRecommendation }: PocketVFRProps) => {
  const { t } = useTranslation();
  const {
    actions: { setErrorProfile, setLoadingRecommendation },
    state: { errorCreatingProfile, loadingRecommendation },
  } = useGlobalContext();

  const formRef = useRef<HTMLFormElement>(null);
  const device = useDevice() as DeviceType;
  const isSmallerDevices = [DeviceType.SmallTablet, DeviceType.Mobile].includes(device);

  const { methods, handleSubmit } = usePocketVFRForm();

  usePostMessage({
    szb_close_size_guide: () => {},
    szb_recommendation_ready: () => setLoadingRecommendation(false),
  });

  useEffect(() => {
    if (errorCreatingProfile) {
      setLoadingRecommendation(false);
    }
  }, [errorCreatingProfile]);

  const handleProfileTryAgain = () => {
    setErrorProfile(null);
  };

  const handleProfileNotFoundClose = () => {
    setErrorProfile(false);
    setLoadingRecommendation(false);
    resetRecommendation();
  };

  return (
    <FormProvider {...methods}>
      <form
        ref={formRef}
        onSubmit={handleSubmit(() => {})}
        className={cn('form-width border border-[#EBEBEB]', {
          'w-[19rem] min-w-full': isSmallerDevices,
        })}
      >
        {isSmallerDevices && (
          <div className="flex justify-center items-center relative py-3 border-b border-[#EBEBEB]">
            <Text size="md" weight="semibold" className="text-center">
              {t('fashion_hint.virtual_fitting_room')}
            </Text>
            <Button variant="blank" className="absolute right-4 top-2 p-2" onClick={onClose} type="button">
              <CloseIcon dimensions={17} viweBox="0 0 24 24" />
            </Button>
          </div>
        )}
        {loadingRecommendation && (
          <div className="flex items-center justify-center h-full">
            <CircularLoading className="h-10 w-10" />
          </div>
        )}
        {errorCreatingProfile && (
          <InfoCard
            textInfo={t('pocket_vfr.error.size_not_found')}
            btnInteractionText={t('generics.try_again')}
            onClose={handleProfileNotFoundClose}
            onAction={handleProfileTryAgain}
          />
        )}
        {!loadingRecommendation && !errorCreatingProfile && <StepManager />}
      </form>
    </FormProvider>
  );
};

import {
  ComplementaryProductsParams,
  ComplementaryProductsResponse,
  SimilarProductsApiResponse,
  SimilarProductsParams,
} from './products';
import { SessionResponse } from './session-types';
import { TrackEventData, TrackEventResponse } from './track-types';

declare module 'sizebay-core-sdk' {
  export interface ClientConfig {
    env: 'production' | 'development';
  }

  export interface Client {
    getSimilarProducts(params: SimilarProductsParams): Promise<SimilarProductsApiResponse>;
    getComplementaryProducts(params: ComplementaryProductsParams): Promise<ComplementaryProductsResponse>;
    track(eventName: string, payload: TrackEventData): Promise<TrackEventResponse>;
    getSessionInfo(): Promise<SessionResponse>;
  }

  export function createClient(config: ClientConfig): Client;
}

import { ButtonHTMLAttributes, ReactNode } from 'react';

export type SwitchSize = 'sm' | 'md' | 'lg';
export type SwitchVariant = 'default' | 'primary' | 'danger';

export interface SwitchProps extends Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'onChange'> {
  /** Whether the switch is checked */
  checked?: boolean;
  /** Default checked state (uncontrolled) */
  defaultChecked?: boolean;
  /** Callback when the checked state changes */
  onCheckedChange?: (checked: boolean) => void;
  /** Whether the switch is disabled */
  disabled?: boolean;
  /** The label for the switch */
  label?: ReactNode;
  /** Additional description text */
  description?: ReactNode;
  /** Required for accessibility when no visible label is provided */
  'aria-label'?: string;
  /** Size variant of the switch */
  size?: SwitchSize;
  /** Visual variant of the switch */
  variant?: SwitchVariant;
}

import { ChangeEvent, InputHTMLAttributes, useEffect, useState } from 'react';
import { cn } from '../../../utils/class-utils';
import { CheckmarkIcon } from '../../icons/checkmark';

interface CheckboxProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type'> {
  checked?: boolean;
  className?: string;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
}

const CHECKBOX_STYLES = {
  CONTAINER: 'relative inline-flex items-center justify-center',
  BASE: 'appearance-none h-4 w-4 rounded-sm outline-none transition-colors border-gray-400 border disabled:cursor-not-allowed disabled:opacity-50',
  CHECKED: 'bg-black',
  UNCHECKED: 'bg-white',
  CHECKMARK: 'absolute pointer-events-none flex items-center justify-center',
};

export const Checkbox = ({ checked = false, className, onChange, ...props }: CheckboxProps) => {
  const [isChecked, setIsChecked] = useState(checked);
  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    onChange?.(event);
    setIsChecked(event.target.checked);
  };

  useEffect(() => {
    setIsChecked(checked);
  }, [checked]);

  return (
    <div className={CHECKBOX_STYLES.CONTAINER}>
      <input
        type="checkbox"
        checked={isChecked}
        onChange={handleChange}
        className={cn(CHECKBOX_STYLES.BASE, isChecked ? CHECKBOX_STYLES.CHECKED : CHECKBOX_STYLES.UNCHECKED, className)}
        {...props}
      />
      {isChecked && (
        <div className={CHECKBOX_STYLES.CHECKMARK}>
          <CheckmarkIcon size={10} className="text-white" />
        </div>
      )}
    </div>
  );
};

Checkbox.displayName = 'Checkbox';

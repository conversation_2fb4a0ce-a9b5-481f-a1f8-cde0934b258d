import { RefObject, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { ProductCard } from '../product-card/product-card';
import { ProductsSkeleton } from '../products-skeleton/products-skeleton';
import { Divider } from '@/components/atoms';
import { TrackEvents } from '@/constants/tracking-events';
import { useTrackEvent } from '@/hooks/use-track-event';
import { CarouselVariant, ProductCardAlignment, ProductCardType } from '@/types/carousel';
import { ComplementaryProduct, ComplementaryProductsBundle, SimilarProduct } from '@/types/products';
import { cn } from '@/utils/class-utils';

interface CarouselContentProps {
  products: SimilarProduct[] | ComplementaryProductsBundle;
  isLoading: boolean;
  contentRef: RefObject<HTMLDivElement> | null;
  type: CarouselVariant;
}

interface ProductItemData {
  itemPosition?: string;
  [key: string]: unknown;
}

interface ProductData {
  product?: ProductItemData;
  [key: string]: unknown;
}

interface ParsedProductData {
  [key: string]: ProductData;
}

const CarouselContentStyles = {
  type: {
    similar: {
      container: 'flex flex-row gap-5 xl:gap-[1.1rem] overflow-auto scrollbar-none snap-x snap-mandatory scroll-smooth',
      item: 'snap-start',
    },
    complementary: {
      container: 'carousel-container-complementary snap-x snap-mandatory scroll-smooth',
      item: 'carousel-item-complementary flex flex-col rounded-[10px] border border-[#D6D6D6] h-fit snap-start',
    },
  },
};

export const CarouselContent = ({ products, isLoading, contentRef, type }: CarouselContentProps) => {
  const { callTrackEvent } = useTrackEvent();
  const { pathname } = useLocation();

  const handleClick = async (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();

    const target = e.target as HTMLElement;
    const productCard = target.closest('.product-card') as HTMLElement;

    if (productCard) {
      const trackData = productCard.getAttribute('data-trackdata');
      if (trackData) {
        const parsedData = JSON.parse(trackData);
        if (parsedData.product?.index !== 0 || parsedData.product?.index === undefined) {
          const payload = {
            eventName:
              pathname === '/similar'
                ? TrackEvents.SIMILARITY_RECOMMENDATION_CLICK
                : TrackEvents.COMPLEMENTARY_RECOMMENDATION_CLICK,
            ...parsedData,
          };
          if (type === CarouselVariant.Complementary) {
            if (parsedData.product?.itemPosition === ProductCardType.BaseProduct) {
              return;
            }

            const siblingProducts = productCard.parentElement?.querySelectorAll('.product-card');
            const siblingProductsArray = Array.from(siblingProducts || []);
            const trackDataArray = siblingProductsArray.map((sibling) => sibling.getAttribute('data-trackdata'));
            const parsedProducts = trackDataArray?.reduce<ParsedProductData>((acc, data) => {
              if (!data) return acc;

              try {
                const parsedData = JSON.parse(data) as ProductData;
                const position = parsedData.product?.itemPosition;

                if (position) {
                  acc[position] = parsedData;
                }

                return acc;
              } catch {
                return acc;
              }
            }, {});

            const positionKeys = [
              ProductCardType.BaseProduct,
              ProductCardType.FirstProduct,
              ProductCardType.SecondaryProduct,
            ] as const;

            positionKeys.forEach((key) => {
              if (parsedProducts?.[key]?.product) {
                payload[key] = { ...parsedProducts[key].product };
              }
            });
          }

          await callTrackEvent(payload);
          window.parent.postMessage(
            {
              id: 'szb_click_product',
              type: type,
              productLink: parsedData.product.link,
            },
            '*'
          );
        }
      }
    }
  };

  const ProductCardRenderer = useCallback(
    ({
      product,
      position,
      index,
    }: {
      product: SimilarProduct | ComplementaryProduct;
      position?: ProductCardType;
      index?: number;
    }) => {
      const alignment =
        type === CarouselVariant.Similar ? ProductCardAlignment.OneColumn : ProductCardAlignment.TwoColumns;
      const productCardProps = {
        ...product,
        imageUrl: product.imageLink,
        imageAlt: product.title,
        name: product.title,
        className: CarouselContentStyles.type[type].item,
        role: 'listitem',
      };

      if (type === CarouselVariant.Similar) {
        Object.assign(productCardProps, {
          pageNum: (product as SimilarProduct).pageNum,
        });
      }

      if (type === CarouselVariant.Complementary) {
        Object.assign(productCardProps, {
          itemPosition: position,
          className: cn(
            index === 0 && 'rounded-t-lg',
            index === 2 && 'rounded-b-lg',
            (product as ComplementaryProduct).itemType !== ProductCardType.BaseProduct && 'cursor-pointer'
          ),
        });
      }

      return (
        <ProductCard
          product={{
            ...productCardProps,
            suitableSizes:
              'suitableSizes' in productCardProps
                ? productCardProps.suitableSizes?.map((size) => ({
                    ...size,
                    value: Boolean(size.value),
                  }))
                : undefined,
          }}
          alignment={alignment}
        />
      );
    },
    [type]
  );

  const renderProducts = useCallback(
    (products: SimilarProduct[] | ComplementaryProductsBundle) => {
      const renderStrategies = {
        similar: (products: SimilarProduct[]) =>
          products.map((product) => (
            <ProductCardRenderer
              key={product.id}
              product={{
                ...product,
              }}
            />
          )),
        complementary: (products: ComplementaryProductsBundle) =>
          products.map((prods, idx) => (
            <div
              className={CarouselContentStyles.type[type].item}
              role="listbox"
              key={`${idx}-${prods.map((p) => p.id).join('-')}`}
            >
              {prods.map((product, index) => {
                return (
                  <>
                    <ProductCardRenderer
                      key={`${idx}-${product.id}-${index}`}
                      product={{
                        ...product,
                      }}
                      position={product.itemType as ProductCardType}
                      index={index}
                    />
                    {index !== prods.length - 1 && <Divider className="w-full flex-none" />}
                  </>
                );
              })}
            </div>
          )),
      };

      return type === 'similar'
        ? renderStrategies[type](products as SimilarProduct[])
        : renderStrategies[type](products as ComplementaryProductsBundle);
    },
    [type, ProductCardRenderer]
  );

  if (isLoading) {
    return (
      <div className={CarouselContentStyles.type[type].container}>
        <ProductsSkeleton type={type} />
      </div>
    );
  }

  const isCarouselCentered =
    type === CarouselVariant.Similar
      ? (products as SimilarProduct[]).length >= 1 && (products as SimilarProduct[]).length <= 3
      : (products as ComplementaryProductsBundle).length === 1;

  return (
    <div
      ref={contentRef}
      role="list"
      className={cn(CarouselContentStyles.type[type].container, isCarouselCentered && 'justify-center')}
      onClick={(e) => handleClick(e)}
    >
      {renderProducts(products)}
    </div>
  );
};

import {
  TEXT_COLORS,
  TEXT_SIZES,
  TEXT_WEIGHTS,
  type TextColor,
  type TextSize,
  type TextWeight,
  type TypographyElement,
} from './text.styles';
import { cn } from '@/utils/class-utils';

interface TypographyProps extends React.HTMLAttributes<HTMLElement> {
  /** The size of the text */
  size: TextSize;
  /** The weight/boldness of the text */
  weight?: TextWeight;
  color?: TextColor;
  /** The HTML element to render (optional, defaults based on variant) */
  as?: TypographyElement;
  /** The content to display */
  children?: React.ReactNode;
  /** Additional CSS classes to apply */
  className?: string;
}

export function Text({
  size,
  weight = 'regular',
  color = 'black',
  as: Element = 'p',
  children,
  className,
  ...props
}: TypographyProps) {
  return (
    <Element className={cn(TEXT_COLORS[color], TEXT_SIZES[size], TEXT_WEIGHTS[weight], className)} {...props}>
      {children}
    </Element>
  );
}

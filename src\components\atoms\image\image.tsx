import { FC, ImgHTMLAttributes } from 'react';
import { classes, cn } from '../../../utils/class-utils';

export interface ImageProps extends ImgHTMLAttributes<HTMLImageElement> {
  /** The source URL of the image */
  src: string;
  /** The alt text for the image */
  alt: string;
  /** Additional CSS classes */
  className?: string;
}

const baseStyles = classes(['object-cover']);

export const Image: FC<ImageProps> = ({ src, alt, className, ...props }) => {
  return <img src={src} alt={alt} className={cn(baseStyles, className)} {...props} />;
};

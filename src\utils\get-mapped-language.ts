import { mappedLanguages } from '../constants/languages';

type response = {
  key: string;
  value: { i18n: string; backend: string };
};

export const getMappedLanguages = (language: string): response => {
  const languages = Object.entries(mappedLanguages);
  const selectedLanguage = languages.find(([key, value]) => key === language || value.i18n === language);

  const value = selectedLanguage?.[1] ?? mappedLanguages.en;

  return {
    key: selectedLanguage?.[0] ?? 'en',
    value: value,
  };
};

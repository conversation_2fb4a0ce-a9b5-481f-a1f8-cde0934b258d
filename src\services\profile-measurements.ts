import { Gender } from '@/types/pocket-virtual-fitting-room';

export interface BodyProps {
  gender: Gender
  bmi: string
  skinType: number
  bodyShapeChest: number
  bodyShapeWaist: number
  bodyShapeHip: number
}

const BUCKET_URL = 'https://static.sizebay.technology';
const BODY_SHAPE_URL = `${BUCKET_URL}/assets/shapes/v4/new`;

export const getBodyDefs = (bodyProps: BodyProps): string => {
  const { gender, skinType, bmi, bodyShapeChest, bodyShapeWaist, bodyShapeHip } = bodyProps;

  return `${BODY_SHAPE_URL}/${skinType}/${gender}/toggle-off/${bmi}/0${bodyShapeChest}0${bodyShapeWaist}0${bodyShapeHip}.jpg`;
};

export const getStripes = async (bodyDefs: string): Promise<string> => {
  const response = await fetch(`${BUCKET_URL}/assets/stripes/1/${bodyDefs}`);
  return await response.text();
};
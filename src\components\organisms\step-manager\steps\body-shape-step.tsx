import { Button, LazyLoadImage, Text } from '@/components/atoms';
import { Step } from '@/components/atoms/step';
import { MinusIcon, PlusIcon } from '@/components/icons';
import { getBodyDefs } from '@/services/profile-measurements';
import { useGlobalContext } from '@/store/global';
import { ClothingStep, Gender, GenericStep, PocketVFRFormData, SizeGuideKeys } from '@/types/pocket-virtual-fitting-room';
import { cn } from '@/utils/class-utils';
import getBmi from '@/utils/get-bmi';
import { normalizeHeight, normalizeWeight } from '@/utils/measurement-conversions';
import { t } from 'i18next';
import { useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
interface BodyShapeStepProps {
  isSmallerDevices: boolean;
}

export const RANGE_MIN = 1;
export const RANGE_MAX = 5;
const INCREMENT = 1;
const DECREMENT = -1;

const shapeSections: SizeGuideKeys[] = ['bodyShapeChest', 'bodyShapeWaist', 'bodyShapeHip'];

const BodyShapeStyles = {
  content: {
    container: 'flex items-start gap-2',
    bodySelectores: 'flex flex-col mt-8 flex-shrink-0 gap-2',
    bodyShape: {
      container: 'flex justify-center overflow-hidden flex-1 h-[19rem]',
      image: 'relative h-auto content-center flex-nowrap aspect-[3/5] inset-0',
    },
  },
  header: { text: 'text-center color-[#555] px-3' },
};

export const BodyShapeStep = ({ isSmallerDevices }: BodyShapeStepProps) => {
  const [bmi, setBmi] = useState('');
  const [bodyImageUrl, setBodyImageUrl] = useState('');
  const { getValues, setValue, handleSubmit } = useFormContext<PocketVFRFormData>();
  const { bodyMeasures, gender, isMetric } = getValues();
  const { height, heightFt, heightIn, weight, bodyShapeChest, bodyShapeWaist, bodyShapeHip } = bodyMeasures || {};
  const {
    actions: { setCurrentStep },
  } = useGlobalContext();

  useEffect(() => {
    let heightNormalized = height || '';
    let weightNormalized = weight || '';
    if (height || !isMetric) {
      if (weight) {
        weightNormalized = normalizeWeight(weight, isMetric);
        setValue('bodyMeasures.weight', weightNormalized);
      }
      if (heightFt && heightIn) {
        heightNormalized = normalizeHeight(heightFt, heightIn).toString();
        setValue('bodyMeasures.height', heightNormalized as string);
      }
    }
    setBmi(getBmi(weightNormalized, heightNormalized, gender as string));
  }, [height, heightFt, heightIn, weight, isMetric, gender]);
  
  useEffect(() => {
    const imageUrl = getBodyDefs({
      gender: gender as Gender,
      bmi,
      skinType: 0,
      bodyShapeChest: bodyShapeChest ? Number(bodyShapeChest) : 3,
      bodyShapeWaist: bodyShapeWaist ? Number(bodyShapeWaist) : 3,
      bodyShapeHip: bodyShapeHip ? Number(bodyShapeHip) : 3,
    });
    setBodyImageUrl(imageUrl);
  }, [gender, bmi, bodyShapeChest, bodyShapeWaist, bodyShapeHip]);

  const handleBodyShapeChange = (shape: SizeGuideKeys, action: number) => {
    console.log('handleBodyShapeChange', shape);

    const shapeKeyMap = {
      bodyShapeChest: 'bodyMeasures.bodyShapeChest' as const,
      bodyShapeWaist: 'bodyMeasures.bodyShapeWaist' as const,
      bodyShapeHip: 'bodyMeasures.bodyShapeHip' as const,
    };

    const shapeKey = shapeKeyMap[shape as keyof typeof shapeKeyMap];
    console.log('shapeKey', shapeKey);
    if (!shapeKey) return;

    const currentValue = Number(getValues(shapeKey));
    const newValue = currentValue + action;
    console.log('currentValue', currentValue);
    console.log('newValue', newValue);
    if (newValue >= RANGE_MIN && newValue <= RANGE_MAX) {
      console.log('setValue', shapeKey, newValue.toString());
      setValue(shapeKey, newValue.toString());
      console.log('getValues', getValues(shapeKey));
    }
  };

  const onSubmit = () => {
    setCurrentStep(GenericStep.SELECT_CATEGORY);
  };

  return (
    <Step.Root>
      <Step.Header>
        <Text size={isSmallerDevices ? 'md' : 'sm'} className={cn(BodyShapeStyles.header.text, 'fh-form__title')}>
          {t('pocket_vfr.clothes.body_shape.caption')}
        </Text>
      </Step.Header>
      <Step.Content className={BodyShapeStyles.content.container}>
        <div className={BodyShapeStyles.content.bodySelectores}>
          {shapeSections.map((shape, key) => (
            <Button key={key} variant="blank" size="sm" className="" onClick={() => handleBodyShapeChange(shape, DECREMENT)}>
              <MinusIcon />
            </Button>
          ))}
        </div>
        <div className={BodyShapeStyles.content.bodyShape.container}>
          <LazyLoadImage src={bodyImageUrl} alt="Body Shape" className={BodyShapeStyles.content.bodyShape.image} />
        </div>
        <div className={BodyShapeStyles.content.bodySelectores}>
          {shapeSections.map((shape, key) => (
            <Button key={key} variant="blank" size="sm" className="" onClick={() => handleBodyShapeChange(shape, INCREMENT)}>
              <PlusIcon />
            </Button>
          ))}
        </div>
      </Step.Content>
      <Step.Footer className="flex gap-2">
        <Button
          type="button"
          fullWidth
          variant="secondary"
          size="sm"
          onClick={() => setCurrentStep(ClothingStep.BODY_MEASURES)}
        >
          {t('pocket_vfr.action_button.go_back')}
        </Button>
        <Button type="button" fullWidth size="sm" onClick={handleSubmit(onSubmit)}>
          {t('pocket_vfr.action_button.save_details')}
        </Button>
      </Step.Footer>
    </Step.Root>
  );
};

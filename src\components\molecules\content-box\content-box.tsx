import React, { createContext } from 'react';
import { ContentBoxFooter } from './content-box-footer';
import { ContentBoxMain } from './content-box-main';
import { ContentBoxProps } from './types';
import { cn } from '@/utils/class-utils';
import { Box } from '@/components/atoms';

const ContentBoxContext = createContext<ContentBoxProps | undefined>(undefined);

export function ContentBox({ children, className }: ContentBoxProps) {
  if (!children) {
    throw new Error('ContentBox component requires children to render.');
  }

  const hasMain = React.Children.toArray(children).some(
    (child) => React.isValidElement(child) && child.type === ContentBoxMain
  );
  const hasFooter = React.Children.toArray(children).some(
    (child) => React.isValidElement(child) && child.type === ContentBoxFooter
  );

  if (!hasMain || !hasFooter) {
    throw new Error('ContenBox component must contain <PERSON> and Footer.');
  }
  return (
    <ContentBoxContext.Provider value={{ children, className }}>
      <Box className={cn('content-box', className)}>{children}</Box>
    </ContentBoxContext.Provider>
  );
}

import React, { ReactNode } from 'react';

export type TooltipPlacement = 'top' | 'bottom' | 'left' | 'right';

export interface TooltipProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'content'> {
  /**
   * The content to display inside the tooltip
   */
  content: ReactNode;
  /**
   * The element that triggers the tooltip
   */
  children: ReactNode;
  /**
   * The position of the tooltip relative to the trigger element
   * @default 'top'
   */
  placement?: TooltipPlacement;
  /**
   * Delay in milliseconds before showing the tooltip
   * @default 0
   */
  delay?: number;
  /**
   * Additional CSS classes to apply to the tooltip
   */
  contentClassName?: string;
  /**
   * Determines if the tooltip is disabled
   * @default false
   */
  disabled?: boolean;
  /**
   * When true, the tooltip will show by default and fade out after the delay time
   * @default false
   */
  showAndFade?: boolean;
  /**
   * Additional CSS classes to apply to the arrow element
   */
  arrowClassName?: string;
  /**
   * Root class name for the tooltip component
   */
  rootClassName?: string;
}

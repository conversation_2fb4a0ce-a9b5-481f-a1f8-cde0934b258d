import { render, screen } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { Image } from '../../../components/atoms';

describe('Image', () => {
  beforeEach(() => {
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const defaultProps = {
    src: 'test-image.jpg',
    alt: 'Test image',
  };

  describe('Rendering', () => {
    it('should render with default props', () => {
      render(<Image {...defaultProps} />);
      const image = screen.getByRole('img');
      expect(image).toBeInTheDocument();
    });

    it('should render with correct src and alt attributes', () => {
      render(<Image {...defaultProps} />);
      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('src', 'test-image.jpg');
      expect(image).toHaveAttribute('alt', 'Test image');
    });
  });

  describe('Styling', () => {
    it('should accept and apply custom className', () => {
      render(<Image {...defaultProps} className="custom-class" />);
      expect(screen.getByRole('img')).toHaveClass('custom-class');
    });

    it('should combine multiple classes correctly', () => {
      render(<Image {...defaultProps} className="w-full h-48 rounded-lg object-cover" />);
      const image = screen.getByRole('img');
      expect(image).toHaveClass('w-full');
      expect(image).toHaveClass('h-48');
      expect(image).toHaveClass('rounded-lg');
      expect(image).toHaveClass('object-cover');
    });
  });

  describe('Accessibility', () => {
    it('should have required alt text for accessibility', () => {
      render(<Image {...defaultProps} />);
      expect(screen.getByRole('img')).toHaveAttribute('alt', 'Test image');
    });

    it('should handle empty alt text for decorative images', () => {
      render(<Image src="decorative.jpg" alt="" />);
      expect(screen.getByRole('img')).toHaveAttribute('alt', '');
    });
  });

  describe('Additional Props', () => {
    it('should pass through additional HTML attributes', () => {
      render(<Image {...defaultProps} loading="lazy" width={300} height={200} data-testid="test-image" />);

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('loading', 'lazy');
      expect(image).toHaveAttribute('width', '300');
      expect(image).toHaveAttribute('height', '200');
      expect(image).toHaveAttribute('data-testid', 'test-image');
    });

    it('should handle onLoad and onError events', () => {
      const onLoad = vi.fn();
      const onError = vi.fn();

      render(<Image {...defaultProps} onLoad={onLoad} onError={onError} />);

      const image = screen.getByRole('img');
      image.dispatchEvent(new Event('load'));
      expect(onLoad).toHaveBeenCalledTimes(1);

      image.dispatchEvent(new Event('error'));
      expect(onError).toHaveBeenCalledTimes(1);
    });
  });

  describe('Error Handling', () => {
    it('should handle missing src attribute', () => {
      // @ts-expect-error - Testing missing required prop
      expect(() => render(<Image alt="Test" />)).toThrow('Image component requires a src prop');
    });

    it('should allow empty alt text for decorative images', () => {
      expect(() => render(<Image src="test.jpg" alt="" />)).not.toThrow();
    });

    it('should handle missing alt attribute', () => {
      // @ts-expect-error - Testing missing required prop
      expect(() => render(<Image src="test.jpg" />)).toThrow('Image component requires an alt prop');
    });
  });
});
